import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { OtpInput } from './otp-input';

describe('OtpInput', () => {
  let component: OtpInput;
  let fixture: ComponentFixture<OtpInput>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [OtpInput, NoopAnimationsModule]
    })
    .compileComponents();

    fixture = TestBed.createComponent(OtpInput);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with empty digits', () => {
    expect(component.otpDigits()).toEqual(['', '', '', '', '', '']);
    expect(component.currentValue()).toBe('');
  });

  it('should update digits when value input changes', () => {
    fixture.componentRef.setInput('value', '123456');
    fixture.detectChanges();
    
    expect(component.otpDigits()).toEqual(['1', '2', '3', '4', '5', '6']);
    expect(component.currentValue()).toBe('123456');
  });

  it('should emit valueChange when digits change', () => {
    let emittedValue: string | undefined;
    component.valueChange.subscribe(value => emittedValue = value);

    const newDigits = ['1', '2', '3', '4', '5', '6'];
    component.otpDigits.set(newDigits);

    expect(emittedValue).toBe('123456');
  });

  it('should emit complete when all digits are filled', () => {
    let completedValue: string | undefined;
    component.complete.subscribe(value => completedValue = value);

    const newDigits = ['1', '2', '3', '4', '5', '6'];
    component.otpDigits.set(newDigits);

    expect(completedValue).toBe('123456');
  });

  it('should handle numeric input only', () => {
    const mockEvent = {
      target: { value: 'a1b2c3' }
    } as any;

    component.onInputChange(mockEvent, 0);
    
    // Should extract only numeric characters
    expect(component.otpDigits()[0]).toBe('1');
  });

  it('should move to next input when digit is entered', () => {
    spyOn(component as any, 'focusInput');
    
    const mockEvent = {
      target: { value: '1' }
    } as any;

    component.onInputChange(mockEvent, 0);
    
    expect((component as any).focusInput).toHaveBeenCalledWith(1);
  });

  it('should handle backspace correctly', () => {
    // Set initial digits
    component.otpDigits.set(['1', '2', '3', '', '', '']);
    
    const mockEvent = {
      key: 'Backspace',
      target: { value: '3' },
      preventDefault: jasmine.createSpy('preventDefault')
    } as any;

    component.onKeyDown(mockEvent, 2);
    
    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(component.otpDigits()[2]).toBe('');
  });

  it('should handle paste correctly', () => {
    const mockEvent = {
      preventDefault: jasmine.createSpy('preventDefault'),
      clipboardData: {
        getData: jasmine.createSpy('getData').and.returnValue('123456')
      }
    } as any;

    component.onPaste(mockEvent, 0);
    
    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(component.otpDigits()).toEqual(['1', '2', '3', '4', '5', '6']);
  });

  it('should clear all inputs when clear method is called', () => {
    component.otpDigits.set(['1', '2', '3', '4', '5', '6']);
    
    component.clear();
    
    expect(component.otpDigits()).toEqual(['', '', '', '', '', '']);
  });

  it('should generate correct indices array', () => {
    expect(component.getIndices()).toEqual([0, 1, 2, 3, 4, 5]);
  });

  it('should handle custom length', () => {
    fixture.componentRef.setInput('length', 4);
    fixture.detectChanges();
    
    expect(component.getIndices()).toEqual([0, 1, 2, 3]);
  });

  it('should handle arrow key navigation', () => {
    spyOn(component as any, 'focusInput');
    
    const leftArrowEvent = {
      key: 'ArrowLeft',
      preventDefault: jasmine.createSpy('preventDefault')
    } as any;

    component.onKeyDown(leftArrowEvent, 2);
    
    expect(leftArrowEvent.preventDefault).toHaveBeenCalled();
    expect((component as any).focusInput).toHaveBeenCalledWith(1);

    const rightArrowEvent = {
      key: 'ArrowRight',
      preventDefault: jasmine.createSpy('preventDefault')
    } as any;

    component.onKeyDown(rightArrowEvent, 2);
    
    expect(rightArrowEvent.preventDefault).toHaveBeenCalled();
    expect((component as any).focusInput).toHaveBeenCalledWith(3);
  });
});
