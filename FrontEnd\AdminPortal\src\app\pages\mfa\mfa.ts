import { Component, signal, computed } from '@angular/core';
import { AuthCard } from '@components/auth-card/auth-card';
import { PasswordInput } from '@components/password-input/password-input';
import { PrimaryButton } from '@components/primary-button/primary-button';


@Component({
  selector: 'app-mfa',
  imports: [
      AuthCard,
      
      PasswordInput,
      PrimaryButton
    ],
  templateUrl: './mfa.html',
  styleUrl: './mfa.scss'
})
export class Mfa { 
  mfaCode = signal<string>('');
  isLoading = signal<boolean>(false);


  isValidForm = computed<boolean>(() =>
    !!(this.mfaCode())
  ); 

  onMfaCodeChange(value: string): void {
    this.mfaCode.set(value);
  }

  onSubmit(): void {
    if (this.isValidForm()) {
      this.isLoading.set(true);
      setTimeout(() => {
        this.isLoading.set(false);
        console.log('Login attempt:', this.mfaCode());
      }, 2000);
    }
  }

}
