import { Component, signal, computed } from '@angular/core';
import { AuthCard } from '@components/auth-card/auth-card';
import { OtpInput } from '@components/otp-input/otp-input';
import { PrimaryButton } from '@components/primary-button/primary-button';


@Component({
  selector: 'app-mfa',
  imports: [
      AuthCard,
      OtpInput,
      PrimaryButton
    ],
  templateUrl: './mfa.html',
  styleUrl: './mfa.scss'
})
export class Mfa { 
  mfaCode = signal<string>('');
  isLoading = signal<boolean>(false);


  isValidForm = computed<boolean>(() =>
    !!(this.mfaCode() && this.mfaCode().length === 6)
  );

  onMfaCodeChange(value: string): void {
    this.mfaCode.set(value);
  }

  onMfaCodeComplete(value: string): void {
    // Auto-submit when MFA code is complete (optional)
    if (value.length === 6 && this.isValidForm()) {
      // Optionally auto-submit here
      // this.onSubmit();
    }
  }

  onSubmit(): void {
    if (this.isValidForm()) {
      this.isLoading.set(true);
      setTimeout(() => {
        this.isLoading.set(false);
        console.log('Login attempt:', this.mfaCode());
      }, 2000);
    }
  }

}
