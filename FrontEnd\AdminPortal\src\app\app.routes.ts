import { Routes } from '@angular/router';
import { Login } from '@pages/login/login';
import { AuthLayout } from '@layouts/auth-layout/auth-layout';
import { AppLayout } from '@layouts/app-layout/app-layout';
import { Mfa } from '@pages/mfa/mfa';
import { ForgotPassword } from '@pages/forgot-password/forgot-password';
import { VerifyOtp } from '@pages/verify-otp/verify-otp';
import { ResetPassword } from '@pages/reset-password/reset-password';
import { Dashboard } from '@pages/dashboard/dashboard';
import { AuthGuard } from '@guards/auth.guard';
import { GuestGuard } from '@guards/guest.guard';

export const routes: Routes = [
    {
        path: '',
        component: AppLayout,
        canActivate: [AuthGuard],
        canActivateChild: [AuthGuard],
        children: [
            { path: 'invoices',   component: Dashboard },
            { path: 'items',   component: Dashboard },
            { path: 'reports',   component: Dashboard },
            { path: 'store-setting',   component: Dashboard },
            { path: '', pathMatch: 'full' ,  component: Dashboard },
        ],
    },
    {
        path: 'auth',
        component: AuthLayout,
        canActivate: [GuestGuard],
        canActivateChild: [GuestGuard],
        children: [
            { path: 'login',   component: Login },
            { path: 'mfa' ,component: Mfa },
            { path: 'forgot-password' ,component: ForgotPassword },
            { path: 'verify-otp' ,component: VerifyOtp },
            { path: 'reset-password' ,component: ResetPassword },
            { path: '', redirectTo: 'login', pathMatch: 'full' },
        ],
    },
];
