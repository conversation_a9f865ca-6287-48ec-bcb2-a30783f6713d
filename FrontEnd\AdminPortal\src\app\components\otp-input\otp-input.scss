// OTP Input Component Styles
.otp-input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.otp-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ivy-neutral-700);
  margin-bottom: 4px;
}

.otp-inputs-wrapper {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: flex-start;
  flex-wrap: nowrap;
  width: 100%;
  max-width: 360px;
  margin: 0 auto;
}

.otp-digit-field {
  width: 48px;
  height: 56px;
  flex: 0 0 48px;

  // Override Material form field styles
  ::ng-deep {
    .mat-mdc-form-field-flex {
      align-items: center;
      justify-content: center;
      height: 56px;
    }

    .mat-mdc-text-field-wrapper {
      padding: 0;
      height: 56px;
    }

    .mat-mdc-form-field-infix {
      padding: 0;
      min-height: 56px;
      border-top: none;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .mat-mdc-form-field-subscript-wrapper {
      display: none; // Hide subscript area
    }
    
    // Outline styles
    .mdc-notched-outline {
      border-color: var(--ivy-neutral-300);
      border-width: 2px;
      border-radius: 8px;
    }
    
    .mdc-notched-outline--notched .mdc-notched-outline__notch {
      border-top: none;
    }
    
    // Focus styles
    &.mat-focused {
      .mdc-notched-outline {
        border-color: var(--ivy-sky-blue);
        border-width: 2px;
      }
    }
    
    // Error styles
    &.mat-form-field-invalid {
      .mdc-notched-outline {
        border-color: var(--ivy-red-500, #ef4444);
      }
    }
    
    // Disabled styles
    &.mat-form-field-disabled {
      .mdc-notched-outline {
        border-color: var(--ivy-neutral-200);
        background-color: var(--ivy-neutral-50);
      }
    }
  }
}

.otp-digit-input {
  text-align: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--ivy-neutral-800);
  caret-color: var(--ivy-sky-blue);

  // Remove default input styles
  border: none;
  outline: none;
  background: transparent;

  // Ensure proper sizing
  width: 100%;
  height: 56px;
  line-height: 56px;
  padding: 0;
  
  &::placeholder {
    color: var(--ivy-neutral-400);
    font-weight: 400;
  }
  
  &:focus {
    outline: none;
  }
  
  &:disabled {
    color: var(--ivy-neutral-400);
    cursor: not-allowed;
  }
  
  // Remove number input spinners
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  
  &[type="number"] {
    -moz-appearance: textfield;
  }
}

.otp-placeholder {
  font-size: 0.875rem;
  color: var(--ivy-neutral-500);
  text-align: center;
  margin-top: -8px;
}

// Responsive design
@media (max-width: 480px) {
  .otp-inputs-wrapper {
    gap: 6px;
    max-width: 300px;
  }

  .otp-digit-field {
    width: 40px;
    height: 48px;
    flex: 0 0 40px;

    ::ng-deep {
      .mat-mdc-form-field-flex {
        height: 48px;
      }

      .mat-mdc-text-field-wrapper {
        height: 48px;
      }

      .mat-mdc-form-field-infix {
        min-height: 48px;
      }

      .otp-digit-input {
        font-size: 1.125rem;
        height: 48px;
        line-height: 48px;
      }
    }
  }
}

@media (max-width: 360px) {
  .otp-inputs-wrapper {
    gap: 4px;
    max-width: 240px;
  }

  .otp-digit-field {
    width: 36px;
    height: 44px;
    flex: 0 0 36px;

    ::ng-deep {
      .mat-mdc-form-field-flex {
        height: 44px;
      }

      .mat-mdc-text-field-wrapper {
        height: 44px;
      }

      .mat-mdc-form-field-infix {
        min-height: 44px;
      }

      .otp-digit-input {
        font-size: 1rem;
        height: 44px;
        line-height: 44px;
      }
    }
  }
}

// Animation for focus transitions
.otp-digit-field {
  transition: all 0.2s ease;
  
  &:hover:not(.mat-form-field-disabled) {
    ::ng-deep .mdc-notched-outline {
      border-color: var(--ivy-neutral-400);
    }
  }
}

// Success state (when all digits are filled)
.otp-input-container.complete {
  .otp-digit-field {
    ::ng-deep .mdc-notched-outline {
      border-color: var(--ivy-green-500, #22c55e);
    }
  }
}

// Loading state
.otp-input-container.loading {
  .otp-digit-input {
    opacity: 0.6;
    pointer-events: none;
  }
}
