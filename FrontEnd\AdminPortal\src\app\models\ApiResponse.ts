export interface LoginApiResponse {
  isSuccess: boolean;
  message: string;
  data?: {
    token: string;
    user: {
      id: string;
      userName: string;
      email: string;
      name: string;
      role: string;
    };
  };
  stateCode: number;
}

export interface ForgotPasswordApiResponse {
  isSuccess: boolean;
  message: string;
  stateCode: number;
  data?: any;
}

export interface VerifyOtpApiResponse {
  isSuccess: boolean;
  message: string;
  stateCode: number;
  data?: {
    token?: string;
    [key: string]: any;
  };
}

export interface ResetPasswordApiResponse {
  isSuccess: boolean;
  message: string;
  stateCode: number;
  data?: any;
}

export interface ApiError {
  success: false;
  message: string;
  errors?: string[];
}
