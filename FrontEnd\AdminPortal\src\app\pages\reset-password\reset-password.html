<app-auth-card
    [title]="'Reset Password'"
    [subtitle]="'Enter your new password'"
    class="max-w-full"
    >
    <div class="flex flex-col gap-4">
        @if(errorMessage()){
        <div  class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
          {{ errorMessage() }}
        </div>
    }
        @if(successMessage()){

        <div  class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md text-sm">
          {{ successMessage() }}
        </div>
    }
        <!-- Email Display -->
        @if(email()){

        <div  class="text-sm text-gray-600 text-center">
          Resetting password for: <strong>{{ email() }}</strong>
        </div>
    }

        <!-- Password Input -->
        <app-password-input
            [label]="'New Password'"
            [placeholder]="'Enter new password'"
            [name]="'password'"
            [value]="password()"
            [required]="true"
            [autocomplete]="'new-password'"
            (valueChange)="onPasswordCodeChange($event)">
        </app-password-input>

        <!-- Password Requirements -->
        @if(password() && !isPasswordStrong()){

        <div  class="text-xs text-gray-600 bg-gray-50 p-3 rounded-md">
          <div class="font-medium mb-1">Password must contain:</div>
          <ul class="list-disc list-inside space-y-1">
            <li>At least 8 characters</li>
            <li>One uppercase letter (A-Z)</li>
            <li>One lowercase letter (a-z)</li>
            <li>One number (0-9)</li>
            <li>One special character {{'(!@#$%^&*)'}}</li>
          </ul>
        </div>
    }
        <!-- Confirm Password Input -->
        <app-password-input
            [label]="'Confirm New Password'"
            [placeholder]="'Confirm new password'"
            [name]="'confirmPassword'"
            [value]="confirmPassword()"
            [required]="true"
            [autocomplete]="'new-password'"
            (valueChange)="onConfirmPasswordCodeChange($event)">
        </app-password-input>

        <!-- Password Match Validation -->
        @if(confirmPassword() && !passwordsMatch()){
            <div   class="text-sm text-red-600">
            Passwords do not match
            </div>
        }
        <!-- Submit Button -->
        <app-primary-button
            [text]="'Reset Password'"
            [type]="'button'"
            [loading]="isLoading()"
            [disabled]="!isValidForm()"
            (buttonClick)="onSubmit()">
        </app-primary-button>

        <!-- Back to Login -->
        <div class="text-center mt-4">
          <a href="#" class="text-sm font-medium text-ivy-sky-blue no-underline hover:text-ivy-sky-blue-dark hover:underline"
             (click)="onBackToLogin()">
            Back to Login
          </a>
        </div>
    </div>
</app-auth-card>