import { createRequire } from 'node:module';
globalThis['require'] ??= createRequire(import.meta.url);
var j=Object.create;var e=Object.defineProperty,k=Object.defineProperties,l=Object.getOwnPropertyDescriptor,m=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertyNames,g=Object.getOwnPropertySymbols,o=Object.getPrototypeOf,i=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var h=(b,a,c)=>a in b?e(b,a,{enumerable:!0,configurable:!0,writable:!0,value:c}):b[a]=c,r=(b,a)=>{for(var c in a||={})i.call(a,c)&&h(b,c,a[c]);if(g)for(var c of g(a))p.call(a,c)&&h(b,c,a[c]);return b},s=(b,a)=>k(b,m(a));var t=(b=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(b,{get:(a,c)=>(typeof require<"u"?require:a)[c]}):b)(function(b){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+b+'" is not supported')});var u=(b,a)=>()=>(a||b((a={exports:{}}).exports,a),a.exports),v=(b,a)=>{for(var c in a)e(b,c,{get:a[c],enumerable:!0})},q=(b,a,c,f)=>{if(a&&typeof a=="object"||typeof a=="function")for(let d of n(a))!i.call(b,d)&&d!==c&&e(b,d,{get:()=>a[d],enumerable:!(f=l(a,d))||f.enumerable});return b};var w=(b,a,c)=>(c=b!=null?j(o(b)):{},q(a||!b||!b.__esModule?e(c,"default",{value:b,enumerable:!0}):c,b));export{r as a,s as b,t as c,u as d,v as e,w as f};
