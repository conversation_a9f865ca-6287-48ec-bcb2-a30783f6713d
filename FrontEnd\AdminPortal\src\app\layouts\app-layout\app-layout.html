<mat-drawer-container class="h-screen overflow-hidden">
    <mat-drawer mode="side" [style.width]="sidebarWidth()" class="mat-app-sidebar" opened>
        <div class="app-sidebar ">
            <app-sidebar [sidebarCollapsed]="sidebarCollapsed()"    (onToggleSidebar)="toggleSidebar()" ></app-sidebar>
        </div>
    </mat-drawer>
    <mat-drawer-content [style.margin-left]="sidebarWidth()">
        <div class="app-main" >
            <div class="app-header">
                <app-header></app-header>
            </div> 
            <div  class="app-container">
                <router-outlet ></router-outlet> 
            </div>
        </div>
    </mat-drawer-content>
</mat-drawer-container>
