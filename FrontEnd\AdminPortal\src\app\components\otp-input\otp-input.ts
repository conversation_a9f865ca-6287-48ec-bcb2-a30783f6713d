import { Component, input, output, signal, effect, ElementRef, ViewChildren, QueryList, AfterViewInit } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-otp-input',
  templateUrl: './otp-input.html',
  styleUrl: './otp-input.scss',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    FormsModule
  ]
})
export class OtpInput implements AfterViewInit {
  // Input signals
  label = input<string>('Verification Code');
  placeholder = input<string>('Enter 6-digit code');
  name = input<string>('otp');
  required = input<boolean>(false);
  value = input<string>('');
  disabled = input<boolean>(false);
  autocomplete = input<string>('one-time-code');
  length = input<number>(6);

  // Output signals
  valueChange = output<string>();
  complete = output<string>();

  // Internal signals
  otpDigits = signal<string[]>(['', '', '', '', '', '']);
  currentValue = signal<string>('');

  // ViewChildren to access input elements
  @ViewChildren('otpInput') otpInputs!: QueryList<ElementRef<HTMLInputElement>>;

  constructor() {
    // Effect to sync currentValue with input value changes
    effect(() => {
      const inputValue = this.value();
      if (inputValue !== this.currentValue()) {
        this.setOtpFromString(inputValue);
      }
    });

    // Effect to update current value when digits change
    effect(() => {
      const digits = this.otpDigits();
      // Only join non-empty digits to create the value
      const filledDigits = digits.filter(digit => digit !== '');
      const newValue = filledDigits.join('');
      this.currentValue.set(newValue);
      this.valueChange.emit(newValue);

      // Emit complete event when all digits are filled
      if (filledDigits.length === this.length()) {
        this.complete.emit(newValue);
      }
    });
  }

  ngAfterViewInit(): void {
    // Focus first input on component load
    setTimeout(() => {
      if (this.otpInputs.first && !this.disabled()) {
        this.otpInputs.first.nativeElement.focus();
      }
    });
  }

  private setOtpFromString(value: string): void {
    const digits = value.split('').slice(0, this.length());
    const paddedDigits = [...digits, ...Array(this.length() - digits.length).fill('')];
    this.otpDigits.set(paddedDigits);
  }

  onInputChange(event: Event, index: number): void {
    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;

    // Only allow numeric input
    value = value.replace(/\D/g, '');

    // Handle multiple characters pasted
    if (value.length > 1) {
      this.handlePastedValue(value, index);
      return;
    }

    // Update the specific digit
    const newDigits = [...this.otpDigits()];
    newDigits[index] = value;
    this.otpDigits.set(newDigits);

    // Move to next input if value is entered
    if (value && index < this.length() - 1) {
      this.focusInput(index + 1);
    }
  }

  onKeyDown(event: KeyboardEvent, index: number): void {
    const inputElement = event.target as HTMLInputElement;

    // Handle backspace
    if (event.key === 'Backspace') {
      event.preventDefault();
      
      if (inputElement.value) {
        // Clear current input
        const newDigits = [...this.otpDigits()];
        newDigits[index] = '';
        this.otpDigits.set(newDigits);
      } else if (index > 0) {
        // Move to previous input and clear it
        this.focusInput(index - 1);
        const newDigits = [...this.otpDigits()];
        newDigits[index - 1] = '';
        this.otpDigits.set(newDigits);
      }
    }

    // Handle arrow keys
    if (event.key === 'ArrowLeft' && index > 0) {
      event.preventDefault();
      this.focusInput(index - 1);
    }

    if (event.key === 'ArrowRight' && index < this.length() - 1) {
      event.preventDefault();
      this.focusInput(index + 1);
    }

    // Handle delete key
    if (event.key === 'Delete') {
      event.preventDefault();
      const newDigits = [...this.otpDigits()];
      newDigits[index] = '';
      this.otpDigits.set(newDigits);
    }
  }

  onPaste(event: ClipboardEvent, index: number): void {
    event.preventDefault();
    const pastedData = event.clipboardData?.getData('text') || '';
    this.handlePastedValue(pastedData, index);
  }

  onFocus(event: FocusEvent): void {
    const inputElement = event.target as HTMLInputElement;
    // Select all text when input is focused
    setTimeout(() => inputElement.select());
  }

  private handlePastedValue(value: string, startIndex: number): void {
    // Extract only numeric characters
    const numericValue = value.replace(/\D/g, '');
    const newDigits = [...this.otpDigits()];

    // Fill digits starting from the current index
    for (let i = 0; i < numericValue.length && (startIndex + i) < this.length(); i++) {
      newDigits[startIndex + i] = numericValue[i];
    }

    this.otpDigits.set(newDigits);

    // Focus the next empty input or the last filled input
    const nextEmptyIndex = newDigits.findIndex((digit, idx) => idx >= startIndex && !digit);
    const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : Math.min(startIndex + numericValue.length - 1, this.length() - 1);
    
    setTimeout(() => this.focusInput(focusIndex));
  }

  private focusInput(index: number): void {
    if (this.otpInputs && this.otpInputs.toArray()[index]) {
      this.otpInputs.toArray()[index].nativeElement.focus();
    }
  }

  // Method to clear all inputs
  clear(): void {
    this.otpDigits.set(Array(this.length()).fill(''));
    if (this.otpInputs.first) {
      this.otpInputs.first.nativeElement.focus();
    }
  }

  // Method to get array of indices for template iteration
  getIndices(): number[] {
    return Array.from({ length: this.length() }, (_, i) => i);
  }

  // Method to check if placeholder should be shown
  shouldShowPlaceholder(): boolean {
    return this.otpDigits().every(digit => digit === '');
  }
}
