ARG NODE_VERSION=22.14.0-alpine 
FROM node:${NODE_VERSION}  AS builder
WORKDIR /work
COPY package.json . 
COPY package-lock.json .
RUN npm install
COPY . . 
RUN addgroup -S nodejs && adduser -S nodejs -G nodejs

# Create the logs directory and give write permission to nodejs user
RUN find /work -path /work/node_modules -prune -o -exec chown nodejs:nodejs {} +
USER nodejs 
EXPOSE 8000
CMD ["sh", "-c", "npm run deploy"]
#CMD ["yarn","start"]
