import { Component, signal, computed, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthCard } from '@components/auth-card/auth-card';
import { OtpInput } from '@components/otp-input/otp-input';
import { PrimaryButton } from '@components/primary-button/primary-button';
import { AuthService } from '@services/auth.service';
import { VerifyOtpModel } from '@models/VerifyOtpModel';

@Component({
  selector: 'app-verify-otp',
  imports: [
    AuthCard,
    OtpInput,
    PrimaryButton
  ],
  templateUrl: './verify-otp.html',
  styleUrl: './verify-otp.scss'
})
export class VerifyOtp implements OnDestroy {
  email = signal<string>('');
  otp = signal<string>('');
  isLoading = signal<boolean>(false);
  successMessage = signal<string>('');
  errorMessage = signal<string>('');

  // Resend OTP timer properties
  resendCountdown = signal<number>(0);
  private countdownInterval: any = null;

  verifyOtpData = computed<VerifyOtpModel>(() => ({
    email: this.email(),
    otp: this.otp()
  }));

  isValidForm = computed<boolean>(() => {
    const email = this.email();
    const otp = this.otp();
    return !!(email && otp && otp.length === 6 && /^\d{6}$/.test(otp));
  });

  // Computed property to check if resend is disabled
  isResendDisabled = computed<boolean>(() =>
    this.isLoading() || this.resendCountdown() > 0
  );

  // Computed property for resend button text
  resendButtonText = computed<string>(() => {
    const countdown = this.resendCountdown();
    return countdown > 0
      ? `Resend in ${countdown}s`
      : "Didn't receive code? Resend";
  });

  constructor(
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    // Get email from query parameters
    this.route.queryParams.subscribe(params => {
      if (params['email']) {
        this.email.set(params['email']);
      }
    });

    // Start countdown timer when component initializes
    this.startResendCountdown();
  }

  ngOnDestroy(): void {
    // Clear the countdown interval when component is destroyed
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
    }
  }

  onOtpChange(value: string): void {
    this.otp.set(value);
    // Clear messages when user starts typing
    this.errorMessage.set('');
    this.successMessage.set('');
  }

  onOtpComplete(value: string): void {
    // Auto-submit when OTP is complete (optional)
    if (value.length === 6 && this.isValidForm()) {
      // Optionally auto-submit here
      // this.onSubmit();
    }
  }

  async onSubmit(): Promise<void> {
    if (this.isValidForm()) {
      this.isLoading.set(true);
      this.errorMessage.set('');
      this.successMessage.set('');
      
      try {
        const result = await this.authService.verifyOtp(this.verifyOtpData());
        
        if (result.success) {
          this.successMessage.set(result.message);
          // Redirect to reset password page with token
          setTimeout(() => {
            this.router.navigate(['/auth/reset-password'], {
              queryParams: {
                email: this.email(),
                verified: 'true',
                token: result.token || '' // Pass token if available
              }
            });
          }, 2000);
        } else {
          this.errorMessage.set(result.message);
        }
      } catch (error) {
        this.errorMessage.set('An unexpected error occurred. Please try again.');
      } finally {
        this.isLoading.set(false);
      }
    }
  }

  onBackToForgotPassword(): void {
    this.router.navigate(['/auth/forgot-password']);
  }

  async onResendOtp(): Promise<void> {
    // Only allow resend if countdown is finished and not loading
    if (this.email() && this.resendCountdown() === 0 && !this.isLoading()) {
      this.isLoading.set(true);
      try {
        const result = await this.authService.forgotPassword({ email: this.email() });
        if (result.success) {
          this.successMessage.set('OTP resent successfully');
          this.errorMessage.set('');
          // Start countdown timer after successful resend
          this.startResendCountdown();
        } else {
          this.errorMessage.set(result.message);
        }
      } catch (error) {
        this.errorMessage.set('Failed to resend OTP. Please try again.');
      } finally {
        this.isLoading.set(false);
      }
    }
  }

  /**
   * Start the 60-second countdown timer for resend OTP
   */
  private startResendCountdown(): void {
    // Clear any existing interval
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval);
    }

    // Set initial countdown to 60 seconds
    this.resendCountdown.set(60);

    // Start the countdown interval
    this.countdownInterval = setInterval(() => {
      const currentCount = this.resendCountdown();
      if (currentCount > 0) {
        this.resendCountdown.set(currentCount - 1);
      } else {
        // Clear interval when countdown reaches 0
        clearInterval(this.countdownInterval);
        this.countdownInterval = null;
      }
    }, 1000);
  }
}
