import { Component, signal, computed } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthCard } from '@components/auth-card/auth-card';
import { FormInput } from '@components/form-input/form-input';
import { PrimaryButton } from '@components/primary-button/primary-button';
import { AuthService } from '@services/auth.service';
import { VerifyOtpModel } from '@models/VerifyOtpModel';

@Component({
  selector: 'app-verify-otp',
  imports: [
    AuthCard,
    FormInput,
    PrimaryButton
  ],
  templateUrl: './verify-otp.html',
  styleUrl: './verify-otp.scss'
})
export class VerifyOtp {
  email = signal<string>('');
  otp = signal<string>('');
  isLoading = signal<boolean>(false);
  successMessage = signal<string>('');
  errorMessage = signal<string>('');

  verifyOtpData = computed<VerifyOtpModel>(() => ({
    email: this.email(),
    otp: this.otp()
  }));

  isValidForm = computed<boolean>(() =>
    !!(this.email() && this.otp() && this.otp().length >= 4)
  );

  constructor(
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    // Get email from query parameters
    this.route.queryParams.subscribe(params => {
      if (params['email']) {
        this.email.set(params['email']);
      }
    });
  }

  onOtpChange(value: string): void {
    // Only allow numeric input and limit to 6 digits
    const numericValue = value.replace(/\D/g, '').slice(0, 6);
    this.otp.set(numericValue);
    // Clear messages when user starts typing
    this.errorMessage.set('');
    this.successMessage.set('');
  }

  async onSubmit(): Promise<void> {
    if (this.isValidForm()) {
      this.isLoading.set(true);
      this.errorMessage.set('');
      this.successMessage.set('');
      
      try {
        const result = await this.authService.verifyOtp(this.verifyOtpData());
        
        if (result.success) {
          this.successMessage.set(result.message);
          // Redirect to reset password page with token
          setTimeout(() => {
            this.router.navigate(['/auth/reset-password'], {
              queryParams: {
                email: this.email(),
                verified: 'true',
                token: result.token || '' // Pass token if available
              }
            });
          }, 2000);
        } else {
          this.errorMessage.set(result.message);
        }
      } catch (error) {
        this.errorMessage.set('An unexpected error occurred. Please try again.');
      } finally {
        this.isLoading.set(false);
      }
    }
  }

  onBackToForgotPassword(): void {
    this.router.navigate(['/auth/forgot-password']);
  }

  async onResendOtp(): Promise<void> {
    if (this.email()) {
      this.isLoading.set(true);
      try {
        const result = await this.authService.forgotPassword({ email: this.email() });
        if (result.success) {
          this.successMessage.set('OTP resent successfully');
          this.errorMessage.set('');
        } else {
          this.errorMessage.set(result.message);
        }
      } catch (error) {
        this.errorMessage.set('Failed to resend OTP. Please try again.');
      } finally {
        this.isLoading.set(false);
      }
    }
  }
}
