{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/list.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/unique-selection-dispatcher-Cewa_Eg3.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/selection-model-BCgC8uEN.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/collections.mjs", "../../../../../../node_modules/@angular/material/fesm2022/pseudo-checkbox-DDmgx3P4.mjs", "../../../../../../node_modules/@angular/material/fesm2022/pseudo-checkbox-module-4F8Up4PL.mjs"], "sourcesContent": ["import { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Directive, signal, Input, NgZone, Injector, ContentChildren, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, ChangeDetectorRef, EventEmitter, Output, forwardRef, Renderer2, NgModule } from '@angular/core';\nimport { Platform, _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { Subscription, merge, Subject } from 'rxjs';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS, R as RippleRenderer } from './ripple-BYgV4oZC.mjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { CdkObserveContent, ObserversModule } from '@angular/cdk/observers';\nimport { MatDividerModule } from './divider.mjs';\nconst _c0 = [\"*\"];\nconst _c1 = \".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\";\nconst _c2 = [\"unscopedContent\"];\nconst _c3 = [\"text\"];\nconst _c4 = [[[\"\", \"matListItemAvatar\", \"\"], [\"\", \"matListItemIcon\", \"\"]], [[\"\", \"matListItemTitle\", \"\"]], [[\"\", \"matListItemLine\", \"\"]], \"*\", [[\"\", \"matListItemMeta\", \"\"]], [[\"mat-divider\"]]];\nconst _c5 = [\"[matListItemAvatar],[matListItemIcon]\", \"[matListItemTitle]\", \"[matListItemLine]\", \"*\", \"[matListItemMeta]\", \"mat-divider\"];\nconst _c6 = [[[\"\", \"matListItemTitle\", \"\"]], [[\"\", \"matListItemLine\", \"\"]], \"*\", [[\"mat-divider\"]], [[\"\", \"matListItemAvatar\", \"\"], [\"\", \"matListItemIcon\", \"\"]]];\nconst _c7 = [\"[matListItemTitle]\", \"[matListItemLine]\", \"*\", \"mat-divider\", \"[matListItemAvatar],[matListItemIcon]\"];\nfunction MatListOption_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 4);\n  }\n}\nfunction MatListOption_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"input\", 12);\n    i0.ɵɵelementStart(2, \"div\", 13);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 14);\n    i0.ɵɵelement(4, \"path\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(5, \"div\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mdc-checkbox--disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.selected)(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction MatListOption_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"input\", 18);\n    i0.ɵɵelementStart(2, \"div\", 19);\n    i0.ɵɵelement(3, \"div\", 20)(4, \"div\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mdc-radio--disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.selected)(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction MatListOption_Conditional_6_ng_template_1_Template(rf, ctx) {}\nfunction MatListOption_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtemplate(1, MatListOption_Conditional_6_ng_template_1_Template, 0, 0, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const checkbox_r3 = i0.ɵɵreference(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", checkbox_r3);\n  }\n}\nfunction MatListOption_Conditional_7_ng_template_1_Template(rf, ctx) {}\nfunction MatListOption_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtemplate(1, MatListOption_Conditional_7_ng_template_1_Template, 0, 0, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const radio_r4 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", radio_r4);\n  }\n}\nfunction MatListOption_Conditional_8_ng_template_0_Template(rf, ctx) {}\nfunction MatListOption_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatListOption_Conditional_8_ng_template_0_Template, 0, 0, \"ng-template\", 6);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const icons_r5 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", icons_r5);\n  }\n}\nfunction MatListOption_Conditional_15_ng_template_1_Template(rf, ctx) {}\nfunction MatListOption_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtemplate(1, MatListOption_Conditional_15_ng_template_1_Template, 0, 0, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const checkbox_r3 = i0.ɵɵreference(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", checkbox_r3);\n  }\n}\nfunction MatListOption_Conditional_16_ng_template_1_Template(rf, ctx) {}\nfunction MatListOption_Conditional_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtemplate(1, MatListOption_Conditional_16_ng_template_1_Template, 0, 0, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const radio_r4 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", radio_r4);\n  }\n}\nfunction MatListOption_Conditional_17_ng_template_0_Template(rf, ctx) {}\nfunction MatListOption_Conditional_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatListOption_Conditional_17_ng_template_0_Template, 0, 0, \"ng-template\", 6);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const icons_r5 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", icons_r5);\n  }\n}\nexport { MatDivider } from './divider.mjs';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { ENTER, SPACE, A, hasModifierKey } from '@angular/cdk/keycodes';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { takeUntil } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-4F8Up4PL.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/bidi';\nimport './pseudo-checkbox-DDmgx3P4.mjs';\n\n/**\n * Injection token that can be used to reference instances of an `ListOption`. It serves\n * as alternative token to an actual implementation which could result in undesired\n * retention of the class or circular references breaking runtime execution.\n * @docs-private\n */\nconst LIST_OPTION = new InjectionToken('ListOption');\n\n/**\n * Directive capturing the title of a list item. A list item usually consists of a\n * title and optional secondary or tertiary lines.\n *\n * Text content for the title never wraps. There can only be a single title per list item.\n */\nclass MatListItemTitle {\n  _elementRef = inject(ElementRef);\n  constructor() {}\n  static ɵfac = function MatListItemTitle_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatListItemTitle)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatListItemTitle,\n    selectors: [[\"\", \"matListItemTitle\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-list-item-title\", \"mdc-list-item__primary-text\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListItemTitle, [{\n    type: Directive,\n    args: [{\n      selector: '[matListItemTitle]',\n      host: {\n        'class': 'mat-mdc-list-item-title mdc-list-item__primary-text'\n      }\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive capturing a line in a list item. A list item usually consists of a\n * title and optional secondary or tertiary lines.\n *\n * Text content inside a line never wraps. There can be at maximum two lines per list item.\n */\nclass MatListItemLine {\n  _elementRef = inject(ElementRef);\n  constructor() {}\n  static ɵfac = function MatListItemLine_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatListItemLine)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatListItemLine,\n    selectors: [[\"\", \"matListItemLine\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-list-item-line\", \"mdc-list-item__secondary-text\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListItemLine, [{\n    type: Directive,\n    args: [{\n      selector: '[matListItemLine]',\n      host: {\n        'class': 'mat-mdc-list-item-line mdc-list-item__secondary-text'\n      }\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive matching an optional meta section for list items.\n *\n * List items can reserve space at the end of an item to display a control,\n * button or additional text content.\n */\nclass MatListItemMeta {\n  static ɵfac = function MatListItemMeta_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatListItemMeta)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatListItemMeta,\n    selectors: [[\"\", \"matListItemMeta\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-list-item-meta\", \"mdc-list-item__end\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListItemMeta, [{\n    type: Directive,\n    args: [{\n      selector: '[matListItemMeta]',\n      host: {\n        'class': 'mat-mdc-list-item-meta mdc-list-item__end'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * @docs-private\n *\n * MDC uses the very intuitively named classes `.mdc-list-item__start` and `.mat-list-item__end` to\n * position content such as icons or checkboxes/radios that comes either before or after the text\n * content respectively. This directive detects the placement of the checkbox/radio and applies the\n * correct MDC class to position the icon/avatar on the opposite side.\n */\nclass _MatListItemGraphicBase {\n  _listOption = inject(LIST_OPTION, {\n    optional: true\n  });\n  constructor() {}\n  _isAlignedAtStart() {\n    // By default, in all list items the graphic is aligned at start. In list options,\n    // the graphic is only aligned at start if the checkbox/radio is at the end.\n    return !this._listOption || this._listOption?._getTogglePosition() === 'after';\n  }\n  static ɵfac = function _MatListItemGraphicBase_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _MatListItemGraphicBase)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatListItemGraphicBase,\n    hostVars: 4,\n    hostBindings: function _MatListItemGraphicBase_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-list-item__start\", ctx._isAlignedAtStart())(\"mdc-list-item__end\", !ctx._isAlignedAtStart());\n      }\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatListItemGraphicBase, [{\n    type: Directive,\n    args: [{\n      host: {\n        // MDC uses intuitively named classes `.mdc-list-item__start` and `.mat-list-item__end` to\n        // position content such as icons or checkboxes/radios that comes either before or after the\n        // text content respectively. This directive detects the placement of the checkbox/radio and\n        // applies the correct MDC class to position the icon/avatar on the opposite side.\n        '[class.mdc-list-item__start]': '_isAlignedAtStart()',\n        '[class.mdc-list-item__end]': '!_isAlignedAtStart()'\n      }\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive matching an optional avatar within a list item.\n *\n * List items can reserve space at the beginning of an item to display an avatar.\n */\nclass MatListItemAvatar extends _MatListItemGraphicBase {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatListItemAvatar_BaseFactory;\n    return function MatListItemAvatar_Factory(__ngFactoryType__) {\n      return (ɵMatListItemAvatar_BaseFactory || (ɵMatListItemAvatar_BaseFactory = i0.ɵɵgetInheritedFactory(MatListItemAvatar)))(__ngFactoryType__ || MatListItemAvatar);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatListItemAvatar,\n    selectors: [[\"\", \"matListItemAvatar\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-list-item-avatar\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListItemAvatar, [{\n    type: Directive,\n    args: [{\n      selector: '[matListItemAvatar]',\n      host: {\n        'class': 'mat-mdc-list-item-avatar'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Directive matching an optional icon within a list item.\n *\n * List items can reserve space at the beginning of an item to display an icon.\n */\nclass MatListItemIcon extends _MatListItemGraphicBase {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatListItemIcon_BaseFactory;\n    return function MatListItemIcon_Factory(__ngFactoryType__) {\n      return (ɵMatListItemIcon_BaseFactory || (ɵMatListItemIcon_BaseFactory = i0.ɵɵgetInheritedFactory(MatListItemIcon)))(__ngFactoryType__ || MatListItemIcon);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatListItemIcon,\n    selectors: [[\"\", \"matListItemIcon\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-list-item-icon\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListItemIcon, [{\n    type: Directive,\n    args: [{\n      selector: '[matListItemIcon]',\n      host: {\n        'class': 'mat-mdc-list-item-icon'\n      }\n    }]\n  }], null, null);\n})();\n\n/** Injection token that can be used to provide the default options for the list module. */\nconst MAT_LIST_CONFIG = new InjectionToken('MAT_LIST_CONFIG');\n\n/** @docs-private */\nclass MatListBase {\n  _isNonInteractive = true;\n  /** Whether ripples for all list items is disabled. */\n  get disableRipple() {\n    return this._disableRipple;\n  }\n  set disableRipple(value) {\n    this._disableRipple = coerceBooleanProperty(value);\n  }\n  _disableRipple = false;\n  /**\n   * Whether the entire list is disabled. When disabled, the list itself and each of its list items\n   * are disabled.\n   */\n  get disabled() {\n    return this._disabled();\n  }\n  set disabled(value) {\n    this._disabled.set(coerceBooleanProperty(value));\n  }\n  _disabled = signal(false);\n  _defaultOptions = inject(MAT_LIST_CONFIG, {\n    optional: true\n  });\n  static ɵfac = function MatListBase_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatListBase)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatListBase,\n    hostVars: 1,\n    hostBindings: function MatListBase_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      disableRipple: \"disableRipple\",\n      disabled: \"disabled\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListBase, [{\n    type: Directive,\n    args: [{\n      host: {\n        '[attr.aria-disabled]': 'disabled'\n      }\n    }]\n  }], null, {\n    disableRipple: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\n/** @docs-private */\nclass MatListItemBase {\n  _elementRef = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _listBase = inject(MatListBase, {\n    optional: true\n  });\n  _platform = inject(Platform);\n  /** Host element for the list item. */\n  _hostElement;\n  /** indicate whether the host element is a button or not */\n  _isButtonElement;\n  /** Whether animations are disabled. */\n  _noopAnimations = _animationsDisabled();\n  _avatars;\n  _icons;\n  /**\n   * The number of lines this list item should reserve space for. If not specified,\n   * lines are inferred based on the projected content.\n   *\n   * Explicitly specifying the number of lines is useful if you want to acquire additional\n   * space and enable the wrapping of text. The unscoped text content of a list item will\n   * always be able to take up the remaining space of the item, unless it represents the title.\n   *\n   * A maximum of three lines is supported as per the Material Design specification.\n   */\n  set lines(lines) {\n    this._explicitLines = coerceNumberProperty(lines, null);\n    this._updateItemLines(false);\n  }\n  _explicitLines = null;\n  /** Whether ripples for list items are disabled. */\n  get disableRipple() {\n    return this.disabled || this._disableRipple || this._noopAnimations || !!this._listBase?.disableRipple;\n  }\n  set disableRipple(value) {\n    this._disableRipple = coerceBooleanProperty(value);\n  }\n  _disableRipple = false;\n  /** Whether the list-item is disabled. */\n  get disabled() {\n    return this._disabled() || !!this._listBase?.disabled;\n  }\n  set disabled(value) {\n    this._disabled.set(coerceBooleanProperty(value));\n  }\n  _disabled = signal(false);\n  _subscriptions = new Subscription();\n  _rippleRenderer = null;\n  /** Whether the list item has unscoped text content. */\n  _hasUnscopedTextContent = false;\n  /**\n   * Implemented as part of `RippleTarget`.\n   * @docs-private\n   */\n  rippleConfig;\n  /**\n   * Implemented as part of `RippleTarget`.\n   * @docs-private\n   */\n  get rippleDisabled() {\n    return this.disableRipple || !!this.rippleConfig.disabled;\n  }\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    this.rippleConfig = globalRippleOptions || {};\n    this._hostElement = this._elementRef.nativeElement;\n    this._isButtonElement = this._hostElement.nodeName.toLowerCase() === 'button';\n    if (this._listBase && !this._listBase._isNonInteractive) {\n      this._initInteractiveListItem();\n    }\n    // If no type attribute is specified for a host `<button>` element, set it to `button`. If a\n    // type attribute is already specified, we do nothing. We do this for backwards compatibility.\n    // TODO: Determine if we intend to continue doing this for the MDC-based list.\n    if (this._isButtonElement && !this._hostElement.hasAttribute('type')) {\n      this._hostElement.setAttribute('type', 'button');\n    }\n  }\n  ngAfterViewInit() {\n    this._monitorProjectedLinesAndTitle();\n    this._updateItemLines(true);\n  }\n  ngOnDestroy() {\n    this._subscriptions.unsubscribe();\n    if (this._rippleRenderer !== null) {\n      this._rippleRenderer._removeTriggerEvents();\n    }\n  }\n  /** Whether the list item has icons or avatars. */\n  _hasIconOrAvatar() {\n    return !!(this._avatars.length || this._icons.length);\n  }\n  _initInteractiveListItem() {\n    this._hostElement.classList.add('mat-mdc-list-item-interactive');\n    this._rippleRenderer = new RippleRenderer(this, this._ngZone, this._hostElement, this._platform, inject(Injector));\n    this._rippleRenderer.setupTriggerEvents(this._hostElement);\n  }\n  /**\n   * Subscribes to changes in the projected title and lines. Triggers a\n   * item lines update whenever a change occurs.\n   */\n  _monitorProjectedLinesAndTitle() {\n    this._ngZone.runOutsideAngular(() => {\n      this._subscriptions.add(merge(this._lines.changes, this._titles.changes).subscribe(() => this._updateItemLines(false)));\n    });\n  }\n  /**\n   * Updates the lines of the list item. Based on the projected user content and optional\n   * explicit lines setting, the visual appearance of the list item is determined.\n   *\n   * This method should be invoked whenever the projected user content changes, or\n   * when the explicit lines have been updated.\n   *\n   * @param recheckUnscopedContent Whether the projected unscoped content should be re-checked.\n   *   The unscoped content is not re-checked for every update as it is a rather expensive check\n   *   for content that is expected to not change very often.\n   */\n  _updateItemLines(recheckUnscopedContent) {\n    // If the updated is triggered too early before the view and content is initialized,\n    // we just skip the update. After view initialization the update is triggered again.\n    if (!this._lines || !this._titles || !this._unscopedContent) {\n      return;\n    }\n    // Re-check the DOM for unscoped text content if requested. This needs to\n    // happen before any computation or sanity checks run as these rely on the\n    // result of whether there is unscoped text content or not.\n    if (recheckUnscopedContent) {\n      this._checkDomForUnscopedTextContent();\n    }\n    // Sanity check the list item lines and title in the content. This is a dev-mode only\n    // check that can be dead-code eliminated by Terser in production.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      sanityCheckListItemContent(this);\n    }\n    const numberOfLines = this._explicitLines ?? this._inferLinesFromContent();\n    const unscopedContentEl = this._unscopedContent.nativeElement;\n    // Update the list item element to reflect the number of lines.\n    this._hostElement.classList.toggle('mat-mdc-list-item-single-line', numberOfLines <= 1);\n    this._hostElement.classList.toggle('mdc-list-item--with-one-line', numberOfLines <= 1);\n    this._hostElement.classList.toggle('mdc-list-item--with-two-lines', numberOfLines === 2);\n    this._hostElement.classList.toggle('mdc-list-item--with-three-lines', numberOfLines === 3);\n    // If there is no title and the unscoped content is the is the only line, the\n    // unscoped text content will be treated as the title of the list-item.\n    if (this._hasUnscopedTextContent) {\n      const treatAsTitle = this._titles.length === 0 && numberOfLines === 1;\n      unscopedContentEl.classList.toggle('mdc-list-item__primary-text', treatAsTitle);\n      unscopedContentEl.classList.toggle('mdc-list-item__secondary-text', !treatAsTitle);\n    } else {\n      unscopedContentEl.classList.remove('mdc-list-item__primary-text');\n      unscopedContentEl.classList.remove('mdc-list-item__secondary-text');\n    }\n  }\n  /**\n   * Infers the number of lines based on the projected user content. This is useful\n   * if no explicit number of lines has been specified on the list item.\n   *\n   * The number of lines is inferred based on whether there is a title, the number of\n   * additional lines (secondary/tertiary). An additional line is acquired if there is\n   * unscoped text content.\n   */\n  _inferLinesFromContent() {\n    let numOfLines = this._titles.length + this._lines.length;\n    if (this._hasUnscopedTextContent) {\n      numOfLines += 1;\n    }\n    return numOfLines;\n  }\n  /** Checks whether the list item has unscoped text content. */\n  _checkDomForUnscopedTextContent() {\n    this._hasUnscopedTextContent = Array.from(this._unscopedContent.nativeElement.childNodes).filter(node => node.nodeType !== node.COMMENT_NODE).some(node => !!(node.textContent && node.textContent.trim()));\n  }\n  static ɵfac = function MatListItemBase_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatListItemBase)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatListItemBase,\n    contentQueries: function MatListItemBase_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatListItemAvatar, 4);\n        i0.ɵɵcontentQuery(dirIndex, MatListItemIcon, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._avatars = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._icons = _t);\n      }\n    },\n    hostVars: 4,\n    hostBindings: function MatListItemBase_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-disabled\", ctx.disabled)(\"disabled\", ctx._isButtonElement && ctx.disabled || null);\n        i0.ɵɵclassProp(\"mdc-list-item--disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      lines: \"lines\",\n      disableRipple: \"disableRipple\",\n      disabled: \"disabled\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListItemBase, [{\n    type: Directive,\n    args: [{\n      host: {\n        '[class.mdc-list-item--disabled]': 'disabled',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.disabled]': '(_isButtonElement && disabled) || null'\n      }\n    }]\n  }], () => [], {\n    _avatars: [{\n      type: ContentChildren,\n      args: [MatListItemAvatar, {\n        descendants: false\n      }]\n    }],\n    _icons: [{\n      type: ContentChildren,\n      args: [MatListItemIcon, {\n        descendants: false\n      }]\n    }],\n    lines: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Sanity checks the configuration of the list item with respect to the amount\n * of lines, whether there is a title, or if there is unscoped text content.\n *\n * The checks are extracted into a top-level function that can be dead-code\n * eliminated by Terser or other optimizers in production mode.\n */\nfunction sanityCheckListItemContent(item) {\n  const numTitles = item._titles.length;\n  const numLines = item._lines.length;\n  if (numTitles > 1) {\n    console.warn('A list item cannot have multiple titles.');\n  }\n  if (numTitles === 0 && numLines > 0) {\n    console.warn('A list item line can only be used if there is a list item title.');\n  }\n  if (numTitles === 0 && item._hasUnscopedTextContent && item._explicitLines !== null && item._explicitLines > 1) {\n    console.warn('A list item cannot have wrapping content without a title.');\n  }\n  if (numLines > 2 || numLines === 2 && item._hasUnscopedTextContent) {\n    console.warn('A list item can have at maximum three lines.');\n  }\n}\nclass MatActionList extends MatListBase {\n  // An navigation list is considered interactive, but does not extend the interactive list\n  // base class. We do this because as per MDC, items of interactive lists are only reachable\n  // through keyboard shortcuts. We want all items for the navigation list to be reachable\n  // through tab key as we do not intend to provide any special accessibility treatment. The\n  // accessibility treatment depends on how the end-user will interact with it.\n  _isNonInteractive = false;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatActionList_BaseFactory;\n    return function MatActionList_Factory(__ngFactoryType__) {\n      return (ɵMatActionList_BaseFactory || (ɵMatActionList_BaseFactory = i0.ɵɵgetInheritedFactory(MatActionList)))(__ngFactoryType__ || MatActionList);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatActionList,\n    selectors: [[\"mat-action-list\"]],\n    hostAttrs: [\"role\", \"group\", 1, \"mat-mdc-action-list\", \"mat-mdc-list-base\", \"mdc-list\"],\n    exportAs: [\"matActionList\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatListBase,\n      useExisting: MatActionList\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatActionList_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatActionList, [{\n    type: Component,\n    args: [{\n      selector: 'mat-action-list',\n      exportAs: 'matActionList',\n      template: '<ng-content></ng-content>',\n      host: {\n        'class': 'mat-mdc-action-list mat-mdc-list-base mdc-list',\n        'role': 'group'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: MatListBase,\n        useExisting: MatActionList\n      }],\n      styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * Injection token that can be used to inject instances of `MatList`. It serves as\n * alternative token to the actual `MatList` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_LIST = new InjectionToken('MatList');\nclass MatList extends MatListBase {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatList_BaseFactory;\n    return function MatList_Factory(__ngFactoryType__) {\n      return (ɵMatList_BaseFactory || (ɵMatList_BaseFactory = i0.ɵɵgetInheritedFactory(MatList)))(__ngFactoryType__ || MatList);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatList,\n    selectors: [[\"mat-list\"]],\n    hostAttrs: [1, \"mat-mdc-list\", \"mat-mdc-list-base\", \"mdc-list\"],\n    exportAs: [\"matList\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatListBase,\n      useExisting: MatList\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatList_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    styles: [_c1],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatList, [{\n    type: Component,\n    args: [{\n      selector: 'mat-list',\n      exportAs: 'matList',\n      template: '<ng-content></ng-content>',\n      host: {\n        'class': 'mat-mdc-list mat-mdc-list-base mdc-list'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: MatListBase,\n        useExisting: MatList\n      }],\n      styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"]\n    }]\n  }], null, null);\n})();\nclass MatListItem extends MatListItemBase {\n  _lines;\n  _titles;\n  _meta;\n  _unscopedContent;\n  _itemText;\n  /** Indicates whether an item in a `<mat-nav-list>` is the currently active page. */\n  get activated() {\n    return this._activated;\n  }\n  set activated(activated) {\n    this._activated = coerceBooleanProperty(activated);\n  }\n  _activated = false;\n  /**\n   * Determine the value of `aria-current`. Return 'page' if this item is an activated anchor tag.\n   * Otherwise, return `null`. This method is safe to use with server-side rendering.\n   */\n  _getAriaCurrent() {\n    return this._hostElement.nodeName === 'A' && this._activated ? 'page' : null;\n  }\n  _hasBothLeadingAndTrailing() {\n    return this._meta.length !== 0 && (this._avatars.length !== 0 || this._icons.length !== 0);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatListItem_BaseFactory;\n    return function MatListItem_Factory(__ngFactoryType__) {\n      return (ɵMatListItem_BaseFactory || (ɵMatListItem_BaseFactory = i0.ɵɵgetInheritedFactory(MatListItem)))(__ngFactoryType__ || MatListItem);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatListItem,\n    selectors: [[\"mat-list-item\"], [\"a\", \"mat-list-item\", \"\"], [\"button\", \"mat-list-item\", \"\"]],\n    contentQueries: function MatListItem_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatListItemLine, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatListItemTitle, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatListItemMeta, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lines = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._titles = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._meta = _t);\n      }\n    },\n    viewQuery: function MatListItem_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._unscopedContent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._itemText = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-list-item\", \"mdc-list-item\"],\n    hostVars: 13,\n    hostBindings: function MatListItem_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-current\", ctx._getAriaCurrent());\n        i0.ɵɵclassProp(\"mdc-list-item--activated\", ctx.activated)(\"mdc-list-item--with-leading-avatar\", ctx._avatars.length !== 0)(\"mdc-list-item--with-leading-icon\", ctx._icons.length !== 0)(\"mdc-list-item--with-trailing-meta\", ctx._meta.length !== 0)(\"mat-mdc-list-item-both-leading-and-trailing\", ctx._hasBothLeadingAndTrailing())(\"_mat-animation-noopable\", ctx._noopAnimations);\n      }\n    },\n    inputs: {\n      activated: \"activated\"\n    },\n    exportAs: [\"matListItem\"],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c5,\n    decls: 10,\n    vars: 0,\n    consts: [[\"unscopedContent\", \"\"], [1, \"mdc-list-item__content\"], [1, \"mat-mdc-list-item-unscoped-content\", 3, \"cdkObserveContent\"], [1, \"mat-focus-indicator\"]],\n    template: function MatListItem_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c4);\n        i0.ɵɵprojection(0);\n        i0.ɵɵelementStart(1, \"span\", 1);\n        i0.ɵɵprojection(2, 1);\n        i0.ɵɵprojection(3, 2);\n        i0.ɵɵelementStart(4, \"span\", 2, 0);\n        i0.ɵɵlistener(\"cdkObserveContent\", function MatListItem_Template_span_cdkObserveContent_4_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._updateItemLines(true));\n        });\n        i0.ɵɵprojection(6, 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵprojection(7, 4);\n        i0.ɵɵprojection(8, 5);\n        i0.ɵɵelement(9, \"div\", 3);\n      }\n    },\n    dependencies: [CdkObserveContent],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListItem, [{\n    type: Component,\n    args: [{\n      selector: 'mat-list-item, a[mat-list-item], button[mat-list-item]',\n      exportAs: 'matListItem',\n      host: {\n        'class': 'mat-mdc-list-item mdc-list-item',\n        '[class.mdc-list-item--activated]': 'activated',\n        '[class.mdc-list-item--with-leading-avatar]': '_avatars.length !== 0',\n        '[class.mdc-list-item--with-leading-icon]': '_icons.length !== 0',\n        '[class.mdc-list-item--with-trailing-meta]': '_meta.length !== 0',\n        // Utility class that makes it easier to target the case where there's both a leading\n        // and a trailing icon. Avoids having to write out all the combinations.\n        '[class.mat-mdc-list-item-both-leading-and-trailing]': '_hasBothLeadingAndTrailing()',\n        '[class._mat-animation-noopable]': '_noopAnimations',\n        '[attr.aria-current]': '_getAriaCurrent()'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [CdkObserveContent],\n      template: \"<ng-content select=\\\"[matListItemAvatar],[matListItemIcon]\\\"></ng-content>\\n\\n<span class=\\\"mdc-list-item__content\\\">\\n  <ng-content select=\\\"[matListItemTitle]\\\"></ng-content>\\n  <ng-content select=\\\"[matListItemLine]\\\"></ng-content>\\n  <span #unscopedContent class=\\\"mat-mdc-list-item-unscoped-content\\\"\\n        (cdkObserveContent)=\\\"_updateItemLines(true)\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n<ng-content select=\\\"[matListItemMeta]\\\"></ng-content>\\n\\n<ng-content select=\\\"mat-divider\\\"></ng-content>\\n\\n<!--\\n  Strong focus indicator element. MDC uses the `::before` pseudo element for the default\\n  focus/hover/selected state, so we need a separate element.\\n-->\\n<div class=\\\"mat-focus-indicator\\\"></div>\\n\"\n    }]\n  }], null, {\n    _lines: [{\n      type: ContentChildren,\n      args: [MatListItemLine, {\n        descendants: true\n      }]\n    }],\n    _titles: [{\n      type: ContentChildren,\n      args: [MatListItemTitle, {\n        descendants: true\n      }]\n    }],\n    _meta: [{\n      type: ContentChildren,\n      args: [MatListItemMeta, {\n        descendants: true\n      }]\n    }],\n    _unscopedContent: [{\n      type: ViewChild,\n      args: ['unscopedContent']\n    }],\n    _itemText: [{\n      type: ViewChild,\n      args: ['text']\n    }],\n    activated: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of an `SelectionList`. It serves\n * as alternative token to an actual implementation which would result in circular references.\n * @docs-private\n */\nconst SELECTION_LIST = new InjectionToken('SelectionList');\nclass MatListOption extends MatListItemBase {\n  _selectionList = inject(SELECTION_LIST);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _lines;\n  _titles;\n  _unscopedContent;\n  /**\n   * Emits when the selected state of the option has changed.\n   * Use to facilitate two-data binding to the `selected` property.\n   * @docs-private\n   */\n  selectedChange = new EventEmitter();\n  /** Whether the label should appear before or after the checkbox/radio. Defaults to 'after' */\n  togglePosition = 'after';\n  /**\n   * Whether the label should appear before or after the checkbox/radio. Defaults to 'after'\n   *\n   * @deprecated Use `togglePosition` instead.\n   * @breaking-change 17.0.0\n   */\n  get checkboxPosition() {\n    return this.togglePosition;\n  }\n  set checkboxPosition(value) {\n    this.togglePosition = value;\n  }\n  /**\n   * Theme color of the list option. This sets the color of the checkbox/radio.\n   * This API is supported in M2 themes only, it has no effect in M3 themes. For color customization\n   * in M3, see https://material.angular.dev/components/list/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get color() {\n    return this._color || this._selectionList.color;\n  }\n  set color(newValue) {\n    this._color = newValue;\n  }\n  _color;\n  /** Value of the option */\n  get value() {\n    return this._value;\n  }\n  set value(newValue) {\n    if (this.selected && newValue !== this.value && this._inputsInitialized) {\n      this.selected = false;\n    }\n    this._value = newValue;\n  }\n  _value;\n  /** Whether the option is selected. */\n  get selected() {\n    return this._selectionList.selectedOptions.isSelected(this);\n  }\n  set selected(value) {\n    const isSelected = coerceBooleanProperty(value);\n    if (isSelected !== this._selected) {\n      this._setSelected(isSelected);\n      if (isSelected || this._selectionList.multiple) {\n        this._selectionList._reportValueChange();\n      }\n    }\n  }\n  _selected = false;\n  /**\n   * This is set to true after the first OnChanges cycle so we don't\n   * clear the value of `selected` in the first cycle.\n   */\n  _inputsInitialized = false;\n  ngOnInit() {\n    const list = this._selectionList;\n    if (list._value && list._value.some(value => list.compareWith(this._value, value))) {\n      this._setSelected(true);\n    }\n    const wasSelected = this._selected;\n    // List options that are selected at initialization can't be reported properly to the form\n    // control. This is because it takes some time until the selection-list knows about all\n    // available options. Also it can happen that the ControlValueAccessor has an initial value\n    // that should be used instead. Deferring the value change report to the next tick ensures\n    // that the form control value is not being overwritten.\n    Promise.resolve().then(() => {\n      if (this._selected || wasSelected) {\n        this.selected = true;\n        this._changeDetectorRef.markForCheck();\n      }\n    });\n    this._inputsInitialized = true;\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    if (this.selected) {\n      // We have to delay this until the next tick in order\n      // to avoid changed after checked errors.\n      Promise.resolve().then(() => {\n        this.selected = false;\n      });\n    }\n  }\n  /** Toggles the selection state of the option. */\n  toggle() {\n    this.selected = !this.selected;\n  }\n  /** Allows for programmatic focusing of the option. */\n  focus() {\n    this._hostElement.focus();\n  }\n  /** Gets the text label of the list option. Used for the typeahead functionality in the list. */\n  getLabel() {\n    const titleElement = this._titles?.get(0)?._elementRef.nativeElement;\n    // If there is no explicit title element, the unscoped text content\n    // is treated as the list item title.\n    const labelEl = titleElement || this._unscopedContent?.nativeElement;\n    return labelEl?.textContent || '';\n  }\n  /** Whether a checkbox is shown at the given position. */\n  _hasCheckboxAt(position) {\n    return this._selectionList.multiple && this._getTogglePosition() === position;\n  }\n  /** Where a radio indicator is shown at the given position. */\n  _hasRadioAt(position) {\n    return !this._selectionList.multiple && this._getTogglePosition() === position && !this._selectionList.hideSingleSelectionIndicator;\n  }\n  /** Whether icons or avatars are shown at the given position. */\n  _hasIconsOrAvatarsAt(position) {\n    return this._hasProjected('icons', position) || this._hasProjected('avatars', position);\n  }\n  /** Gets whether the given type of element is projected at the specified position. */\n  _hasProjected(type, position) {\n    // If the checkbox/radio is shown at the specified position, neither icons or\n    // avatars can be shown at the position.\n    return this._getTogglePosition() !== position && (type === 'avatars' ? this._avatars.length !== 0 : this._icons.length !== 0);\n  }\n  _handleBlur() {\n    this._selectionList._onTouched();\n  }\n  /** Gets the current position of the checkbox/radio. */\n  _getTogglePosition() {\n    return this.togglePosition || 'after';\n  }\n  /**\n   * Sets the selected state of the option.\n   * @returns Whether the value has changed.\n   */\n  _setSelected(selected) {\n    if (selected === this._selected) {\n      return false;\n    }\n    this._selected = selected;\n    if (selected) {\n      this._selectionList.selectedOptions.select(this);\n    } else {\n      this._selectionList.selectedOptions.deselect(this);\n    }\n    this.selectedChange.emit(selected);\n    this._changeDetectorRef.markForCheck();\n    return true;\n  }\n  /**\n   * Notifies Angular that the option needs to be checked in the next change detection run.\n   * Mainly used to trigger an update of the list option if the disabled state of the selection\n   * list changed.\n   */\n  _markForCheck() {\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Toggles the option's value based on a user interaction. */\n  _toggleOnInteraction() {\n    if (!this.disabled) {\n      if (this._selectionList.multiple) {\n        this.selected = !this.selected;\n        this._selectionList._emitChangeEvent([this]);\n      } else if (!this.selected) {\n        this.selected = true;\n        this._selectionList._emitChangeEvent([this]);\n      }\n    }\n  }\n  /** Sets the tabindex of the list option. */\n  _setTabindex(value) {\n    this._hostElement.setAttribute('tabindex', value + '');\n  }\n  _hasBothLeadingAndTrailing() {\n    const hasLeading = this._hasProjected('avatars', 'before') || this._hasProjected('icons', 'before') || this._hasCheckboxAt('before') || this._hasRadioAt('before');\n    const hasTrailing = this._hasProjected('icons', 'after') || this._hasProjected('avatars', 'after') || this._hasCheckboxAt('after') || this._hasRadioAt('after');\n    return hasLeading && hasTrailing;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatListOption_BaseFactory;\n    return function MatListOption_Factory(__ngFactoryType__) {\n      return (ɵMatListOption_BaseFactory || (ɵMatListOption_BaseFactory = i0.ɵɵgetInheritedFactory(MatListOption)))(__ngFactoryType__ || MatListOption);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatListOption,\n    selectors: [[\"mat-list-option\"]],\n    contentQueries: function MatListOption_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatListItemLine, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatListItemTitle, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lines = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._titles = _t);\n      }\n    },\n    viewQuery: function MatListOption_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._unscopedContent = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"option\", 1, \"mat-mdc-list-item\", \"mat-mdc-list-option\", \"mdc-list-item\"],\n    hostVars: 27,\n    hostBindings: function MatListOption_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"blur\", function MatListOption_blur_HostBindingHandler() {\n          return ctx._handleBlur();\n        })(\"click\", function MatListOption_click_HostBindingHandler() {\n          return ctx._toggleOnInteraction();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-selected\", ctx.selected);\n        i0.ɵɵclassProp(\"mdc-list-item--selected\", ctx.selected && !ctx._selectionList.multiple && ctx._selectionList.hideSingleSelectionIndicator)(\"mdc-list-item--with-leading-avatar\", ctx._hasProjected(\"avatars\", \"before\"))(\"mdc-list-item--with-leading-icon\", ctx._hasProjected(\"icons\", \"before\"))(\"mdc-list-item--with-trailing-icon\", ctx._hasProjected(\"icons\", \"after\"))(\"mat-mdc-list-option-with-trailing-avatar\", ctx._hasProjected(\"avatars\", \"after\"))(\"mdc-list-item--with-leading-checkbox\", ctx._hasCheckboxAt(\"before\"))(\"mdc-list-item--with-trailing-checkbox\", ctx._hasCheckboxAt(\"after\"))(\"mdc-list-item--with-leading-radio\", ctx._hasRadioAt(\"before\"))(\"mdc-list-item--with-trailing-radio\", ctx._hasRadioAt(\"after\"))(\"mat-mdc-list-item-both-leading-and-trailing\", ctx._hasBothLeadingAndTrailing())(\"mat-accent\", ctx.color !== \"primary\" && ctx.color !== \"warn\")(\"mat-warn\", ctx.color === \"warn\")(\"_mat-animation-noopable\", ctx._noopAnimations);\n      }\n    },\n    inputs: {\n      togglePosition: \"togglePosition\",\n      checkboxPosition: \"checkboxPosition\",\n      color: \"color\",\n      value: \"value\",\n      selected: \"selected\"\n    },\n    outputs: {\n      selectedChange: \"selectedChange\"\n    },\n    exportAs: [\"matListOption\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatListItemBase,\n      useExisting: MatListOption\n    }, {\n      provide: LIST_OPTION,\n      useExisting: MatListOption\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c7,\n    decls: 20,\n    vars: 4,\n    consts: [[\"icons\", \"\"], [\"checkbox\", \"\"], [\"radio\", \"\"], [\"unscopedContent\", \"\"], [1, \"mdc-list-item__start\", \"mat-mdc-list-option-checkbox-before\"], [1, \"mdc-list-item__start\", \"mat-mdc-list-option-radio-before\"], [3, \"ngTemplateOutlet\"], [1, \"mdc-list-item__content\"], [1, \"mat-mdc-list-item-unscoped-content\", 3, \"cdkObserveContent\"], [1, \"mdc-list-item__end\"], [1, \"mat-focus-indicator\"], [1, \"mdc-checkbox\"], [\"type\", \"checkbox\", 1, \"mdc-checkbox__native-control\", 3, \"checked\", \"disabled\"], [1, \"mdc-checkbox__background\"], [\"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-checkbox__checkmark\"], [\"fill\", \"none\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-checkbox__checkmark-path\"], [1, \"mdc-checkbox__mixedmark\"], [1, \"mdc-radio\"], [\"type\", \"radio\", 1, \"mdc-radio__native-control\", 3, \"checked\", \"disabled\"], [1, \"mdc-radio__background\"], [1, \"mdc-radio__outer-circle\"], [1, \"mdc-radio__inner-circle\"]],\n    template: function MatListOption_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c6);\n        i0.ɵɵtemplate(0, MatListOption_ng_template_0_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, MatListOption_ng_template_2_Template, 6, 4, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(4, MatListOption_ng_template_4_Template, 5, 4, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵconditionalCreate(6, MatListOption_Conditional_6_Template, 2, 1, \"span\", 4)(7, MatListOption_Conditional_7_Template, 2, 1, \"span\", 5);\n        i0.ɵɵconditionalCreate(8, MatListOption_Conditional_8_Template, 1, 1, null, 6);\n        i0.ɵɵelementStart(9, \"span\", 7);\n        i0.ɵɵprojection(10);\n        i0.ɵɵprojection(11, 1);\n        i0.ɵɵelementStart(12, \"span\", 8, 3);\n        i0.ɵɵlistener(\"cdkObserveContent\", function MatListOption_Template_span_cdkObserveContent_12_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._updateItemLines(true));\n        });\n        i0.ɵɵprojection(14, 2);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵconditionalCreate(15, MatListOption_Conditional_15_Template, 2, 1, \"span\", 9)(16, MatListOption_Conditional_16_Template, 2, 1, \"span\", 9);\n        i0.ɵɵconditionalCreate(17, MatListOption_Conditional_17_Template, 1, 1, null, 6);\n        i0.ɵɵprojection(18, 3);\n        i0.ɵɵelement(19, \"div\", 10);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵconditional(ctx._hasCheckboxAt(\"before\") ? 6 : ctx._hasRadioAt(\"before\") ? 7 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx._hasIconsOrAvatarsAt(\"before\") ? 8 : -1);\n        i0.ɵɵadvance(7);\n        i0.ɵɵconditional(ctx._hasCheckboxAt(\"after\") ? 15 : ctx._hasRadioAt(\"after\") ? 16 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx._hasIconsOrAvatarsAt(\"after\") ? 17 : -1);\n      }\n    },\n    dependencies: [NgTemplateOutlet, CdkObserveContent],\n    styles: [\".mat-mdc-list-option-with-trailing-avatar.mdc-list-item,[dir=rtl] .mat-mdc-list-option-with-trailing-avatar.mdc-list-item{padding-left:0;padding-right:0}.mat-mdc-list-option-with-trailing-avatar .mdc-list-item__end{margin-left:16px;margin-right:16px;width:40px;height:40px}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mat-mdc-list-option-with-trailing-avatar .mdc-list-item__end{border-radius:50%}.mat-mdc-list-option .mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mat-mdc-list-option .mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mat-checkbox-state-layer-size, 40px);height:var(--mat-checkbox-state-layer-size, 40px);top:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);right:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mat-mdc-list-option .mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox--disabled{opacity:.5}}.mat-mdc-list-option .mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mat-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2)}.mat-mdc-list-option .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-list-option .mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox__checkmark{color:CanvasText}}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__checkmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__checkmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mat-mdc-list-option .mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mat-mdc-list-option .mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox__mixedmark{margin:0 1px}}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-list-option .mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color;padding:calc((var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-list-option .mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mat-mdc-list-option .mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px);top:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2);left:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-list-option .mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;top:0;right:0;left:0;cursor:inherit;z-index:1;width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle{transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background{cursor:default}.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-list-option .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-list-option .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-list-option .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle{transform:scale(0.5);transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-list-option._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-list-option._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-list-option .mdc-checkbox__native-control,.mat-mdc-list-option .mdc-radio__native-control{display:none}@media(forced-colors: active){.mat-mdc-list-option.mdc-list-item--selected::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.mat-mdc-list-option.mdc-list-item--selected [dir=rtl]::after{right:auto;left:16px}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-list-option',\n      exportAs: 'matListOption',\n      host: {\n        'class': 'mat-mdc-list-item mat-mdc-list-option mdc-list-item',\n        'role': 'option',\n        // As per MDC, only list items without checkbox or radio indicator should receive the\n        // `--selected` class.\n        '[class.mdc-list-item--selected]': 'selected && !_selectionList.multiple && _selectionList.hideSingleSelectionIndicator',\n        // Based on the checkbox/radio position and whether there are icons or avatars, we apply MDC's\n        // list-item `--leading` and `--trailing` classes.\n        '[class.mdc-list-item--with-leading-avatar]': '_hasProjected(\"avatars\", \"before\")',\n        '[class.mdc-list-item--with-leading-icon]': '_hasProjected(\"icons\", \"before\")',\n        '[class.mdc-list-item--with-trailing-icon]': '_hasProjected(\"icons\", \"after\")',\n        '[class.mat-mdc-list-option-with-trailing-avatar]': '_hasProjected(\"avatars\", \"after\")',\n        // Based on the checkbox/radio position, we apply the `--leading` or `--trailing` MDC classes\n        // which ensure that the checkbox/radio is positioned correctly within the list item.\n        '[class.mdc-list-item--with-leading-checkbox]': '_hasCheckboxAt(\"before\")',\n        '[class.mdc-list-item--with-trailing-checkbox]': '_hasCheckboxAt(\"after\")',\n        '[class.mdc-list-item--with-leading-radio]': '_hasRadioAt(\"before\")',\n        '[class.mdc-list-item--with-trailing-radio]': '_hasRadioAt(\"after\")',\n        // Utility class that makes it easier to target the case where there's both a leading\n        // and a trailing icon. Avoids having to write out all the combinations.\n        '[class.mat-mdc-list-item-both-leading-and-trailing]': '_hasBothLeadingAndTrailing()',\n        '[class.mat-accent]': 'color !== \"primary\" && color !== \"warn\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class._mat-animation-noopable]': '_noopAnimations',\n        '[attr.aria-selected]': 'selected',\n        '(blur)': '_handleBlur()',\n        '(click)': '_toggleOnInteraction()'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: MatListItemBase,\n        useExisting: MatListOption\n      }, {\n        provide: LIST_OPTION,\n        useExisting: MatListOption\n      }],\n      imports: [NgTemplateOutlet, CdkObserveContent],\n      template: \"<!--\\n  Save icons and the pseudo checkbox/radio so that they can be re-used in the template without\\n  duplication. Also content can only be injected once so we need to extract icons/avatars\\n  into a template since we use it in multiple places.\\n-->\\n<ng-template #icons>\\n  <ng-content select=\\\"[matListItemAvatar],[matListItemIcon]\\\">\\n  </ng-content>\\n</ng-template>\\n\\n<ng-template #checkbox>\\n  <div class=\\\"mdc-checkbox\\\" [class.mdc-checkbox--disabled]=\\\"disabled\\\">\\n    <input type=\\\"checkbox\\\" class=\\\"mdc-checkbox__native-control\\\"\\n           [checked]=\\\"selected\\\" [disabled]=\\\"disabled\\\"/>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n  </div>\\n</ng-template>\\n\\n<ng-template #radio>\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <input type=\\\"radio\\\" class=\\\"mdc-radio__native-control\\\"\\n           [checked]=\\\"selected\\\" [disabled]=\\\"disabled\\\"/>\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n  </div>\\n</ng-template>\\n\\n@if (_hasCheckboxAt('before')) {\\n  <!-- Container for the checkbox at start. -->\\n  <span class=\\\"mdc-list-item__start mat-mdc-list-option-checkbox-before\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"checkbox\\\"></ng-template>\\n  </span>\\n} @else if (_hasRadioAt('before')) {\\n  <!-- Container for the radio at the start. -->\\n  <span class=\\\"mdc-list-item__start mat-mdc-list-option-radio-before\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"radio\\\"></ng-template>\\n  </span>\\n}\\n<!-- Conditionally renders icons/avatars before the list item text. -->\\n@if (_hasIconsOrAvatarsAt('before')) {\\n  <ng-template [ngTemplateOutlet]=\\\"icons\\\"></ng-template>\\n}\\n\\n<!-- Text -->\\n<span class=\\\"mdc-list-item__content\\\">\\n  <ng-content select=\\\"[matListItemTitle]\\\"></ng-content>\\n  <ng-content select=\\\"[matListItemLine]\\\"></ng-content>\\n  <span #unscopedContent class=\\\"mat-mdc-list-item-unscoped-content\\\"\\n        (cdkObserveContent)=\\\"_updateItemLines(true)\\\">\\n    <ng-content></ng-content>\\n  </span>\\n</span>\\n\\n@if (_hasCheckboxAt('after')) {\\n  <!-- Container for the checkbox at the end. -->\\n  <span class=\\\"mdc-list-item__end\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"checkbox\\\"></ng-template>\\n  </span>\\n} @else if (_hasRadioAt('after')) {\\n  <!-- Container for the radio at the end. -->\\n  <span class=\\\"mdc-list-item__end\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"radio\\\"></ng-template>\\n  </span>\\n}\\n\\n<!-- Conditionally renders icons/avatars after the list item text. -->\\n@if (_hasIconsOrAvatarsAt('after')) {\\n  <ng-template [ngTemplateOutlet]=\\\"icons\\\"></ng-template>\\n}\\n\\n<!-- Divider -->\\n<ng-content select=\\\"mat-divider\\\"></ng-content>\\n\\n<!--\\n  Strong focus indicator element. MDC uses the `::before` pseudo element for the default\\n  focus/hover/selected state, so we need a separate element.\\n-->\\n<div class=\\\"mat-focus-indicator\\\"></div>\\n\",\n      styles: [\".mat-mdc-list-option-with-trailing-avatar.mdc-list-item,[dir=rtl] .mat-mdc-list-option-with-trailing-avatar.mdc-list-item{padding-left:0;padding-right:0}.mat-mdc-list-option-with-trailing-avatar .mdc-list-item__end{margin-left:16px;margin-right:16px;width:40px;height:40px}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mat-mdc-list-option-with-trailing-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mat-mdc-list-option-with-trailing-avatar .mdc-list-item__end{border-radius:50%}.mat-mdc-list-option .mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mat-mdc-list-option .mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mat-checkbox-state-layer-size, 40px);height:var(--mat-checkbox-state-layer-size, 40px);top:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);right:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mat-mdc-list-option .mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox--disabled{opacity:.5}}.mat-mdc-list-option .mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mat-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2)}.mat-mdc-list-option .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-list-option .mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mat-mdc-list-option .mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox__checkmark{color:CanvasText}}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__checkmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__checkmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mat-mdc-list-option .mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mat-mdc-list-option .mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mat-mdc-list-option .mdc-checkbox__mixedmark{margin:0 1px}}.mat-mdc-list-option .mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mat-mdc-list-option .mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mat-mdc-list-option .mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-list-option .mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color;padding:calc((var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-list-option .mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mat-mdc-list-option .mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px);top:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2);left:calc(-1*(var(--mat-radio-state-layer-size, 40px) - 20px)/2)}.mat-mdc-list-option .mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-list-option .mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;top:0;right:0;left:0;cursor:inherit;z-index:1;width:var(--mat-radio-state-layer-size, 40px);height:var(--mat-radio-state-layer-size, 40px)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle{transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-unselected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-unselected-icon-opacity, 0.38)}.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background{cursor:default}.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__inner-circle,.mat-mdc-list-option .mdc-radio__native-control:disabled+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-disabled-selected-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-radio-disabled-selected-icon-opacity, 0.38)}.mat-mdc-list-option .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background>.mdc-radio__outer-circle{border-color:var(--mat-radio-unselected-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-list-option .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__outer-circle,.mat-mdc-list-option .mdc-radio__native-control:enabled:checked+.mdc-radio__background>.mdc-radio__inner-circle{border-color:var(--mat-radio-selected-icon-color, var(--mat-sys-primary))}.mat-mdc-list-option .mdc-radio__native-control:checked+.mdc-radio__background>.mdc-radio__inner-circle{transform:scale(0.5);transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-list-option._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-list-option._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-list-option._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__start>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-list-option._mat-animation-noopable>.mdc-list-item__end>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-list-option .mdc-checkbox__native-control,.mat-mdc-list-option .mdc-radio__native-control{display:none}@media(forced-colors: active){.mat-mdc-list-option.mdc-list-item--selected::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}.mat-mdc-list-option.mdc-list-item--selected [dir=rtl]::after{right:auto;left:16px}}\\n\"]\n    }]\n  }], null, {\n    _lines: [{\n      type: ContentChildren,\n      args: [MatListItemLine, {\n        descendants: true\n      }]\n    }],\n    _titles: [{\n      type: ContentChildren,\n      args: [MatListItemTitle, {\n        descendants: true\n      }]\n    }],\n    _unscopedContent: [{\n      type: ViewChild,\n      args: ['unscopedContent']\n    }],\n    selectedChange: [{\n      type: Output\n    }],\n    togglePosition: [{\n      type: Input\n    }],\n    checkboxPosition: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatListSubheaderCssMatStyler {\n  static ɵfac = function MatListSubheaderCssMatStyler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatListSubheaderCssMatStyler)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatListSubheaderCssMatStyler,\n    selectors: [[\"\", \"mat-subheader\", \"\"], [\"\", \"matSubheader\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-subheader\", \"mdc-list-group__subheader\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListSubheaderCssMatStyler, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-subheader], [matSubheader]',\n      // TODO(mmalerba): MDC's subheader font looks identical to the list item font, figure out why and\n      //  make a change in one of the repos to visually distinguish.\n      host: {\n        'class': 'mat-mdc-subheader mdc-list-group__subheader'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Injection token that can be used to inject instances of `MatNavList`. It serves as\n * alternative token to the actual `MatNavList` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_NAV_LIST = new InjectionToken('MatNavList');\nclass MatNavList extends MatListBase {\n  // An navigation list is considered interactive, but does not extend the interactive list\n  // base class. We do this because as per MDC, items of interactive lists are only reachable\n  // through keyboard shortcuts. We want all items for the navigation list to be reachable\n  // through tab key as we do not intend to provide any special accessibility treatment. The\n  // accessibility treatment depends on how the end-user will interact with it.\n  _isNonInteractive = false;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatNavList_BaseFactory;\n    return function MatNavList_Factory(__ngFactoryType__) {\n      return (ɵMatNavList_BaseFactory || (ɵMatNavList_BaseFactory = i0.ɵɵgetInheritedFactory(MatNavList)))(__ngFactoryType__ || MatNavList);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatNavList,\n    selectors: [[\"mat-nav-list\"]],\n    hostAttrs: [\"role\", \"navigation\", 1, \"mat-mdc-nav-list\", \"mat-mdc-list-base\", \"mdc-list\"],\n    exportAs: [\"matNavList\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatListBase,\n      useExisting: MatNavList\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatNavList_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    styles: [_c1],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNavList, [{\n    type: Component,\n    args: [{\n      selector: 'mat-nav-list',\n      exportAs: 'matNavList',\n      template: '<ng-content></ng-content>',\n      host: {\n        'class': 'mat-mdc-nav-list mat-mdc-list-base mdc-list',\n        'role': 'navigation'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: MatListBase,\n        useExisting: MatNavList\n      }],\n      styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"]\n    }]\n  }], null, null);\n})();\nconst MAT_SELECTION_LIST_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSelectionList),\n  multi: true\n};\n/** Change event that is being fired whenever the selected state of an option changes. */\nclass MatSelectionListChange {\n  source;\n  options;\n  constructor(/** Reference to the selection list that emitted the event. */\n  source, /** Reference to the options that have been changed. */\n  options) {\n    this.source = source;\n    this.options = options;\n  }\n}\nclass MatSelectionList extends MatListBase {\n  _element = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _renderer = inject(Renderer2);\n  _initialized = false;\n  _keyManager;\n  _listenerCleanups;\n  /** Emits when the list has been destroyed. */\n  _destroyed = new Subject();\n  /** Whether the list has been destroyed. */\n  _isDestroyed;\n  /** View to model callback that should be called whenever the selected options change. */\n  _onChange = _ => {};\n  _items;\n  /** Emits a change event whenever the selected state of an option changes. */\n  selectionChange = new EventEmitter();\n  /**\n   * Theme color of the selection list. This sets the checkbox color for all\n   * list options. This API is supported in M2 themes only, it has no effect in\n   * M3 themes. For color customization in M3, see https://material.angular.dev/components/list/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color = 'accent';\n  /**\n   * Function used for comparing an option against the selected value when determining which\n   * options should appear as selected. The first argument is the value of an options. The second\n   * one is a value from the selected value. A boolean must be returned.\n   */\n  compareWith = (a1, a2) => a1 === a2;\n  /** Whether selection is limited to one or multiple items (default multiple). */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    const newValue = coerceBooleanProperty(value);\n    if (newValue !== this._multiple) {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && this._initialized) {\n        throw new Error('Cannot change `multiple` mode of mat-selection-list after initialization.');\n      }\n      this._multiple = newValue;\n      this.selectedOptions = new SelectionModel(this._multiple, this.selectedOptions.selected);\n    }\n  }\n  _multiple = true;\n  /** Whether radio indicator for all list items is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n  }\n  _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n  /** The currently selected options. */\n  selectedOptions = new SelectionModel(this._multiple);\n  /** Keeps track of the currently-selected value. */\n  _value;\n  /** View to model callback that should be called if the list or its options lost focus. */\n  _onTouched = () => {};\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  constructor() {\n    super();\n    this._isNonInteractive = false;\n  }\n  ngAfterViewInit() {\n    // Mark the selection list as initialized so that the `multiple`\n    // binding can no longer be changed.\n    this._initialized = true;\n    this._setupRovingTabindex();\n    // These events are bound outside the zone, because they don't change\n    // any change-detected properties and they can trigger timeouts.\n    this._ngZone.runOutsideAngular(() => {\n      this._listenerCleanups = [this._renderer.listen(this._element.nativeElement, 'focusin', this._handleFocusin), this._renderer.listen(this._element.nativeElement, 'focusout', this._handleFocusout)];\n    });\n    if (this._value) {\n      this._setOptionsFromValues(this._value);\n    }\n    this._watchForSelectionChange();\n  }\n  ngOnChanges(changes) {\n    const disabledChanges = changes['disabled'];\n    const disableRippleChanges = changes['disableRipple'];\n    const hideSingleSelectionIndicatorChanges = changes['hideSingleSelectionIndicator'];\n    if (disableRippleChanges && !disableRippleChanges.firstChange || disabledChanges && !disabledChanges.firstChange || hideSingleSelectionIndicatorChanges && !hideSingleSelectionIndicatorChanges.firstChange) {\n      this._markOptionsForCheck();\n    }\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._listenerCleanups?.forEach(current => current());\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._isDestroyed = true;\n  }\n  /** Focuses the selection list. */\n  focus(options) {\n    this._element.nativeElement.focus(options);\n  }\n  /** Selects all of the options. Returns the options that changed as a result. */\n  selectAll() {\n    return this._setAllOptionsSelected(true);\n  }\n  /** Deselects all of the options. Returns the options that changed as a result. */\n  deselectAll() {\n    return this._setAllOptionsSelected(false);\n  }\n  /** Reports a value change to the ControlValueAccessor */\n  _reportValueChange() {\n    // Stop reporting value changes after the list has been destroyed. This avoids\n    // cases where the list might wrongly reset its value once it is removed, but\n    // the form control is still live.\n    if (this.options && !this._isDestroyed) {\n      const value = this._getSelectedOptionValues();\n      this._onChange(value);\n      this._value = value;\n    }\n  }\n  /** Emits a change event if the selected state of an option changed. */\n  _emitChangeEvent(options) {\n    this.selectionChange.emit(new MatSelectionListChange(this, options));\n  }\n  /** Implemented as part of ControlValueAccessor. */\n  writeValue(values) {\n    this._value = values;\n    if (this.options) {\n      this._setOptionsFromValues(values || []);\n    }\n  }\n  /** Implemented as a part of ControlValueAccessor. */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n    this._markOptionsForCheck();\n  }\n  /**\n   * Whether the *entire* selection list is disabled. When true, each list item is also disabled\n   * and each list item is removed from the tab order (has tabindex=\"-1\").\n   */\n  get disabled() {\n    return this._selectionListDisabled();\n  }\n  set disabled(value) {\n    // Update the disabled state of this list. Write to `this._selectionListDisabled` instead of\n    // `super.disabled`. That is to avoid closure compiler compatibility issues with assigning to\n    // a super property.\n    this._selectionListDisabled.set(coerceBooleanProperty(value));\n    if (this._selectionListDisabled()) {\n      this._keyManager?.setActiveItem(-1);\n    }\n  }\n  _selectionListDisabled = signal(false);\n  /** Implemented as part of ControlValueAccessor. */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /** Implemented as part of ControlValueAccessor. */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /** Watches for changes in the selected state of the options and updates the list accordingly. */\n  _watchForSelectionChange() {\n    this.selectedOptions.changed.pipe(takeUntil(this._destroyed)).subscribe(event => {\n      // Sync external changes to the model back to the options.\n      for (let item of event.added) {\n        item.selected = true;\n      }\n      for (let item of event.removed) {\n        item.selected = false;\n      }\n      if (!this._containsFocus()) {\n        this._resetActiveOption();\n      }\n    });\n  }\n  /** Sets the selected options based on the specified values. */\n  _setOptionsFromValues(values) {\n    this.options.forEach(option => option._setSelected(false));\n    values.forEach(value => {\n      const correspondingOption = this.options.find(option => {\n        // Skip options that are already in the model. This allows us to handle cases\n        // where the same primitive value is selected multiple times.\n        return option.selected ? false : this.compareWith(option.value, value);\n      });\n      if (correspondingOption) {\n        correspondingOption._setSelected(true);\n      }\n    });\n  }\n  /** Returns the values of the selected options. */\n  _getSelectedOptionValues() {\n    return this.options.filter(option => option.selected).map(option => option.value);\n  }\n  /** Marks all the options to be checked in the next change detection run. */\n  _markOptionsForCheck() {\n    if (this.options) {\n      this.options.forEach(option => option._markForCheck());\n    }\n  }\n  /**\n   * Sets the selected state on all of the options\n   * and emits an event if anything changed.\n   */\n  _setAllOptionsSelected(isSelected, skipDisabled) {\n    // Keep track of whether anything changed, because we only want to\n    // emit the changed event when something actually changed.\n    const changedOptions = [];\n    this.options.forEach(option => {\n      if ((!skipDisabled || !option.disabled) && option._setSelected(isSelected)) {\n        changedOptions.push(option);\n      }\n    });\n    if (changedOptions.length) {\n      this._reportValueChange();\n    }\n    return changedOptions;\n  }\n  // Note: This getter exists for backwards compatibility. The `_items` query list\n  // cannot be named `options` as it will be picked up by the interactive list base.\n  /** The option components contained within this selection-list. */\n  get options() {\n    return this._items;\n  }\n  /** Handles keydown events within the list. */\n  _handleKeydown(event) {\n    const activeItem = this._keyManager.activeItem;\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this._keyManager.isTyping() && activeItem && !activeItem.disabled) {\n      event.preventDefault();\n      activeItem._toggleOnInteraction();\n    } else if (event.keyCode === A && this.multiple && !this._keyManager.isTyping() && hasModifierKey(event, 'ctrlKey', 'metaKey')) {\n      const shouldSelect = this.options.some(option => !option.disabled && !option.selected);\n      event.preventDefault();\n      this._emitChangeEvent(this._setAllOptionsSelected(shouldSelect, true));\n    } else {\n      this._keyManager.onKeydown(event);\n    }\n  }\n  /** Handles focusout events within the list. */\n  _handleFocusout = () => {\n    // Focus takes a while to update so we have to wrap our call in a timeout.\n    setTimeout(() => {\n      if (!this._containsFocus()) {\n        this._resetActiveOption();\n      }\n    });\n  };\n  /** Handles focusin events within the list. */\n  _handleFocusin = event => {\n    if (this.disabled) {\n      return;\n    }\n    const activeIndex = this._items.toArray().findIndex(item => item._elementRef.nativeElement.contains(event.target));\n    if (activeIndex > -1) {\n      this._setActiveOption(activeIndex);\n    } else {\n      this._resetActiveOption();\n    }\n  };\n  /**\n   * Sets up the logic for maintaining the roving tabindex.\n   *\n   * `skipPredicate` determines if key manager should avoid putting a given list item in the tab\n   * index. Allow disabled list items to receive focus to align with WAI ARIA recommendation.\n   * Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n   * makes a few exceptions for compound widgets.\n   *\n   * From [Developing a Keyboard Interface](\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n   *   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n   *   Listbox...\"\n   */\n  _setupRovingTabindex() {\n    this._keyManager = new FocusKeyManager(this._items).withHomeAndEnd().withTypeAhead().withWrap().skipPredicate(() => this.disabled);\n    // Set the initial focus.\n    this._resetActiveOption();\n    // Move the tabindex to the currently-focused list item.\n    this._keyManager.change.subscribe(activeItemIndex => this._setActiveOption(activeItemIndex));\n    // If the active item is removed from the list, reset back to the first one.\n    this._items.changes.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      const activeItem = this._keyManager.activeItem;\n      if (!activeItem || this._items.toArray().indexOf(activeItem) === -1) {\n        this._resetActiveOption();\n      }\n    });\n  }\n  /**\n   * Sets an option as active.\n   * @param index Index of the active option. If set to -1, no option will be active.\n   */\n  _setActiveOption(index) {\n    this._items.forEach((item, itemIndex) => item._setTabindex(itemIndex === index ? 0 : -1));\n    this._keyManager.updateActiveItem(index);\n  }\n  /**\n   * Resets the active option. When the list is disabled, remove all options from to the tab order.\n   * Otherwise, focus the first selected option.\n   */\n  _resetActiveOption() {\n    if (this.disabled) {\n      this._setActiveOption(-1);\n      return;\n    }\n    const activeItem = this._items.find(item => item.selected && !item.disabled) || this._items.first;\n    this._setActiveOption(activeItem ? this._items.toArray().indexOf(activeItem) : -1);\n  }\n  /** Returns whether the focus is currently within the list. */\n  _containsFocus() {\n    const activeElement = _getFocusedElementPierceShadowDom();\n    return activeElement && this._element.nativeElement.contains(activeElement);\n  }\n  static ɵfac = function MatSelectionList_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelectionList)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSelectionList,\n    selectors: [[\"mat-selection-list\"]],\n    contentQueries: function MatSelectionList_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatListOption, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._items = _t);\n      }\n    },\n    hostAttrs: [\"role\", \"listbox\", 1, \"mat-mdc-selection-list\", \"mat-mdc-list-base\", \"mdc-list\"],\n    hostVars: 1,\n    hostBindings: function MatSelectionList_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function MatSelectionList_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-multiselectable\", ctx.multiple);\n      }\n    },\n    inputs: {\n      color: \"color\",\n      compareWith: \"compareWith\",\n      multiple: \"multiple\",\n      hideSingleSelectionIndicator: \"hideSingleSelectionIndicator\",\n      disabled: \"disabled\"\n    },\n    outputs: {\n      selectionChange: \"selectionChange\"\n    },\n    exportAs: [\"matSelectionList\"],\n    features: [i0.ɵɵProvidersFeature([MAT_SELECTION_LIST_VALUE_ACCESSOR, {\n      provide: MatListBase,\n      useExisting: MatSelectionList\n    }, {\n      provide: SELECTION_LIST,\n      useExisting: MatSelectionList\n    }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 0,\n    template: function MatSelectionList_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n      }\n    },\n    styles: [_c1],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectionList, [{\n    type: Component,\n    args: [{\n      selector: 'mat-selection-list',\n      exportAs: 'matSelectionList',\n      host: {\n        'class': 'mat-mdc-selection-list mat-mdc-list-base mdc-list',\n        'role': 'listbox',\n        '[attr.aria-multiselectable]': 'multiple',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      template: '<ng-content></ng-content>',\n      encapsulation: ViewEncapsulation.None,\n      providers: [MAT_SELECTION_LIST_VALUE_ACCESSOR, {\n        provide: MatListBase,\n        useExisting: MatSelectionList\n      }, {\n        provide: SELECTION_LIST,\n        useExisting: MatSelectionList\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mdc-list{margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item{display:flex;position:relative;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer;padding-left:16px;padding-right:16px;background-color:var(--mat-list-list-item-container-color, transparent);border-radius:var(--mat-list-list-item-container-shape, var(--mat-sys-corner-none))}.mdc-list-item.mdc-list-item--selected{background-color:var(--mat-list-list-item-selected-container-color)}.mdc-list-item:focus{outline:0}.mdc-list-item.mdc-list-item--disabled{cursor:auto}.mdc-list-item.mdc-list-item--with-one-line{height:var(--mat-list-list-item-one-line-container-height, 48px)}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines{height:var(--mat-list-list-item-two-line-container-height, 64px)}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines{height:var(--mat-list-list-item-three-line-container-height, 88px)}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--selected::before,.mdc-list-item.mdc-list-item--selected:focus::before,.mdc-list-item:not(.mdc-list-item--selected):focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;content:\\\"\\\";pointer-events:none}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-leading-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-leading-icon-size, 24px);height:var(--mat-list-list-item-leading-icon-size, 24px);margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:32px;margin-right:16px}.mdc-list-item--with-leading-icon:hover .mdc-list-item__start{color:var(--mat-list-list-item-hover-leading-icon-color)}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:var(--mat-list-list-item-leading-avatar-size, 40px);height:var(--mat-list-list-item-leading-avatar-size, 40px);margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item--with-leading-avatar .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px;border-radius:50%}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item--with-trailing-meta .mdc-list-item__end{font-family:var(--mat-list-list-item-trailing-supporting-text-font, var(--mat-sys-label-small-font));line-height:var(--mat-list-list-item-trailing-supporting-text-line-height, var(--mat-sys-label-small-line-height));font-size:var(--mat-list-list-item-trailing-supporting-text-size, var(--mat-sys-label-small-size));font-weight:var(--mat-list-list-item-trailing-supporting-text-weight, var(--mat-sys-label-small-weight));letter-spacing:var(--mat-list-list-item-trailing-supporting-text-tracking, var(--mat-sys-label-small-tracking))}.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-trailing-icon-color, var(--mat-sys-on-surface-variant));width:var(--mat-list-list-item-trailing-icon-size, 24px);height:var(--mat-list-list-item-trailing-icon-size, 24px)}.mdc-list-item--with-trailing-icon:hover .mdc-list-item__end{color:var(--mat-list-list-item-hover-trailing-icon-color)}.mdc-list-item.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:var(--mat-list-list-item-trailing-supporting-text-color, var(--mat-sys-on-surface-variant))}.mdc-list-item--selected.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-selected-trailing-icon-color, var(--mat-sys-primary))}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;color:var(--mat-list-list-item-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-list-list-item-label-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-list-list-item-label-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-list-list-item-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-list-list-item-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-list-list-item-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-list-item:hover .mdc-list-item__primary-text{color:var(--mat-list-list-item-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:focus .mdc-list-item__primary-text{color:var(--mat-list-list-item-focus-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;color:var(--mat-list-list-item-supporting-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-list-list-item-supporting-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-list-list-item-supporting-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-list-list-item-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-list-list-item-supporting-text-weight, var(--mat-sys-body-medium-weight));letter-spacing:var(--mat-list-list-item-supporting-text-tracking, var(--mat-sys-body-medium-tracking))}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:16px}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:16px;padding-right:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before,.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-icon.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:0;padding-right:0}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-webkit-user-select:none;user-select:none;margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:16px;margin-right:28px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;line-height:normal;align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before,.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:24px;margin-right:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start,.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:16px;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:0;padding-right:16px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-left:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item--with-leading-avatar,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-icon,[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item--with-leading-avatar{padding-right:0}.mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:8px;margin-right:24px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-group__subheader{margin:.75rem 16px}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:1}.mdc-list-item--disabled .mdc-list-item__primary-text,.mdc-list-item--disabled .mdc-list-item__secondary-text{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:var(--mat-list-list-item-disabled-leading-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-leading-icon-opacity, 0.38)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:var(--mat-list-list-item-disabled-trailing-icon-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-trailing-icon-opacity, 0.38)}.mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing,[dir=rtl] .mat-mdc-list-item.mat-mdc-list-item-both-leading-and-trailing{padding-left:0;padding-right:0}.mdc-list-item.mdc-list-item--disabled .mdc-list-item__primary-text{color:var(--mat-list-list-item-disabled-label-text-color, var(--mat-sys-on-surface))}.mdc-list-item:hover::before{background-color:var(--mat-list-list-item-hover-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mdc-list-item.mdc-list-item--disabled::before{background-color:var(--mat-list-list-item-disabled-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-disabled-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item:focus::before{background-color:var(--mat-list-list-item-focus-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mat-list-list-item-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-list-item--disabled .mdc-radio,.mdc-list-item--disabled .mdc-checkbox{opacity:var(--mat-list-list-item-disabled-label-text-opacity, 0.3)}.mdc-list-item--with-leading-avatar .mat-mdc-list-item-avatar{border-radius:var(--mat-list-list-item-leading-avatar-shape, var(--mat-sys-corner-full));background-color:var(--mat-list-list-item-leading-avatar-color, var(--mat-sys-primary-container))}.mat-mdc-list-item-icon{font-size:var(--mat-list-list-item-leading-icon-size, 24px)}@media(forced-colors: active){a.mdc-list-item--activated::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}a.mdc-list-item--activated [dir=rtl]::after{right:auto;left:16px}}.mat-mdc-list-base{display:block}.mat-mdc-list-base .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item__end,.mat-mdc-list-base .mdc-list-item__content{pointer-events:auto}.mat-mdc-list-item,.mat-mdc-list-option{width:100%;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-list-item:not(.mat-mdc-list-item-interactive),.mat-mdc-list-option:not(.mat-mdc-list-item-interactive){cursor:default}.mat-mdc-list-item .mat-divider-inset,.mat-mdc-list-option .mat-divider-inset{position:absolute;left:0;right:0;bottom:0}.mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,.mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-left:72px}[dir=rtl] .mat-mdc-list-item .mat-mdc-list-item-avatar~.mat-divider-inset,[dir=rtl] .mat-mdc-list-option .mat-mdc-list-item-avatar~.mat-divider-inset{margin-right:72px}.mat-mdc-list-item-interactive::before{top:0;left:0;right:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;pointer-events:none;border-radius:inherit}.mat-mdc-list-item>.mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-list-item:focus>.mat-focus-indicator::before{content:\\\"\\\"}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-line.mdc-list-item__secondary-text{white-space:nowrap;line-height:normal}.mat-mdc-list-item.mdc-list-item--with-three-lines .mat-mdc-list-item-unscoped-content.mdc-list-item__secondary-text{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}mat-action-list button{background:none;color:inherit;border:none;font:inherit;outline:inherit;-webkit-tap-highlight-color:rgba(0,0,0,0);text-align:start}mat-action-list button::-moz-focus-inner{border:0}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-inline-start:var(--mat-list-list-item-leading-icon-start-space, 16px);margin-inline-end:var(--mat-list-list-item-leading-icon-end-space, 16px)}.mat-mdc-nav-list .mat-mdc-list-item{border-radius:var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full));--mat-focus-indicator-border-radius: var(--mat-list-active-indicator-shape, var(--mat-sys-corner-full))}.mat-mdc-nav-list .mat-mdc-list-item.mdc-list-item--activated{background-color:var(--mat-list-active-indicator-color, var(--mat-sys-secondary-container))}\\n\"]\n    }]\n  }], () => [], {\n    _items: [{\n      type: ContentChildren,\n      args: [MatListOption, {\n        descendants: true\n      }]\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    color: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    multiple: [{\n      type: Input\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\nclass MatListModule {\n  static ɵfac = function MatListModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatListModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatListModule,\n    imports: [ObserversModule, MatCommonModule, MatRippleModule, MatPseudoCheckboxModule, MatList, MatActionList, MatNavList, MatSelectionList, MatListItem, MatListOption, MatListSubheaderCssMatStyler, MatListItemAvatar, MatListItemIcon, MatListItemLine, MatListItemTitle, MatListItemMeta],\n    exports: [MatList, MatActionList, MatNavList, MatSelectionList, MatListItem, MatListOption, MatListItemAvatar, MatListItemIcon, MatListSubheaderCssMatStyler, MatDividerModule, MatListItemLine, MatListItemTitle, MatListItemMeta]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ObserversModule, MatCommonModule, MatRippleModule, MatPseudoCheckboxModule, MatDividerModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatListModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ObserversModule, MatCommonModule, MatRippleModule, MatPseudoCheckboxModule, MatList, MatActionList, MatNavList, MatSelectionList, MatListItem, MatListOption, MatListSubheaderCssMatStyler, MatListItemAvatar, MatListItemIcon, MatListItemLine, MatListItemTitle, MatListItemMeta],\n      exports: [MatList, MatActionList, MatNavList, MatSelectionList, MatListItem, MatListOption, MatListItemAvatar, MatListItemIcon, MatListSubheaderCssMatStyler, MatDividerModule, MatListItemLine, MatListItemTitle, MatListItemMeta]\n    }]\n  }], null, null);\n})();\nexport { MAT_LIST, MAT_LIST_CONFIG, MAT_NAV_LIST, MAT_SELECTION_LIST_VALUE_ACCESSOR, MatActionList, MatList, MatListItem, MatListItemAvatar, MatListItemIcon, MatListItemLine, MatListItemMeta, MatListItemTitle, MatListModule, MatListOption, MatListSubheaderCssMatStyler, MatNavList, MatSelectionList, MatSelectionListChange, SELECTION_LIST, _MatListItemGraphicBase };\n", "import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n  _listeners = [];\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n  notify(id, name) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n  listen(listener) {\n    this._listeners.push(listener);\n    return () => {\n      this._listeners = this._listeners.filter(registered => {\n        return listener !== registered;\n      });\n    };\n  }\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n  static ɵfac = function UniqueSelectionDispatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UniqueSelectionDispatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: UniqueSelectionDispatcher,\n    factory: UniqueSelectionDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UniqueSelectionDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { UniqueSelectionDispatcher as U };\n", "import { Subject } from 'rxjs';\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n    _multiple;\n    _emitChanges;\n    compareWith;\n    /** Currently-selected values. */\n    _selection = new Set();\n    /** Keeps track of the deselected options that haven't been emitted by the change event. */\n    _deselectedToEmit = [];\n    /** Keeps track of the selected options that haven't been emitted by the change event. */\n    _selectedToEmit = [];\n    /** Cache for the array value of the selected items. */\n    _selected;\n    /** Selected values. */\n    get selected() {\n        if (!this._selected) {\n            this._selected = Array.from(this._selection.values());\n        }\n        return this._selected;\n    }\n    /** Event emitted when the value has changed. */\n    changed = new Subject();\n    constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n        this._multiple = _multiple;\n        this._emitChanges = _emitChanges;\n        this.compareWith = compareWith;\n        if (initiallySelectedValues && initiallySelectedValues.length) {\n            if (_multiple) {\n                initiallySelectedValues.forEach(value => this._markSelected(value));\n            }\n            else {\n                this._markSelected(initiallySelectedValues[0]);\n            }\n            // Clear the array in order to avoid firing the change event for preselected values.\n            this._selectedToEmit.length = 0;\n        }\n    }\n    /**\n     * Selects a value or an array of values.\n     * @param values The values to select\n     * @return Whether the selection changed as a result of this call\n     */\n    select(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._markSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Deselects a value or an array of values.\n     * @param values The values to deselect\n     * @return Whether the selection changed as a result of this call\n     */\n    deselect(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Sets the selected values\n     * @param values The new selected values\n     * @return Whether the selection changed as a result of this call\n     */\n    setSelection(...values) {\n        this._verifyValueAssignment(values);\n        const oldValues = this.selected;\n        const newSelectedSet = new Set(values.map(value => this._getConcreteValue(value)));\n        values.forEach(value => this._markSelected(value));\n        oldValues\n            .filter(value => !newSelectedSet.has(this._getConcreteValue(value, newSelectedSet)))\n            .forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Toggles a value between selected and deselected.\n     * @param value The value to toggle\n     * @return Whether the selection changed as a result of this call\n     */\n    toggle(value) {\n        return this.isSelected(value) ? this.deselect(value) : this.select(value);\n    }\n    /**\n     * Clears all of the selected values.\n     * @param flushEvent Whether to flush the changes in an event.\n     *   If false, the changes to the selection will be flushed along with the next event.\n     * @return Whether the selection changed as a result of this call\n     */\n    clear(flushEvent = true) {\n        this._unmarkAll();\n        const changed = this._hasQueuedChanges();\n        if (flushEvent) {\n            this._emitChangeEvent();\n        }\n        return changed;\n    }\n    /**\n     * Determines whether a value is selected.\n     */\n    isSelected(value) {\n        return this._selection.has(this._getConcreteValue(value));\n    }\n    /**\n     * Determines whether the model does not have a value.\n     */\n    isEmpty() {\n        return this._selection.size === 0;\n    }\n    /**\n     * Determines whether the model has a value.\n     */\n    hasValue() {\n        return !this.isEmpty();\n    }\n    /**\n     * Sorts the selected values based on a predicate function.\n     */\n    sort(predicate) {\n        if (this._multiple && this.selected) {\n            this._selected.sort(predicate);\n        }\n    }\n    /**\n     * Gets whether multiple values can be selected.\n     */\n    isMultipleSelection() {\n        return this._multiple;\n    }\n    /** Emits a change event and clears the records of selected and deselected values. */\n    _emitChangeEvent() {\n        // Clear the selected values so they can be re-cached.\n        this._selected = null;\n        if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n            this.changed.next({\n                source: this,\n                added: this._selectedToEmit,\n                removed: this._deselectedToEmit,\n            });\n            this._deselectedToEmit = [];\n            this._selectedToEmit = [];\n        }\n    }\n    /** Selects a value. */\n    _markSelected(value) {\n        value = this._getConcreteValue(value);\n        if (!this.isSelected(value)) {\n            if (!this._multiple) {\n                this._unmarkAll();\n            }\n            if (!this.isSelected(value)) {\n                this._selection.add(value);\n            }\n            if (this._emitChanges) {\n                this._selectedToEmit.push(value);\n            }\n        }\n    }\n    /** Deselects a value. */\n    _unmarkSelected(value) {\n        value = this._getConcreteValue(value);\n        if (this.isSelected(value)) {\n            this._selection.delete(value);\n            if (this._emitChanges) {\n                this._deselectedToEmit.push(value);\n            }\n        }\n    }\n    /** Clears out the selected values. */\n    _unmarkAll() {\n        if (!this.isEmpty()) {\n            this._selection.forEach(value => this._unmarkSelected(value));\n        }\n    }\n    /**\n     * Verifies the value assignment and throws an error if the specified value array is\n     * including multiple values while the selection model is not supporting multiple values.\n     */\n    _verifyValueAssignment(values) {\n        if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMultipleValuesInSingleSelectionError();\n        }\n    }\n    /** Whether there are queued up change to be emitted. */\n    _hasQueuedChanges() {\n        return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n    }\n    /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n    _getConcreteValue(inputValue, selection) {\n        if (!this.compareWith) {\n            return inputValue;\n        }\n        else {\n            selection = selection ?? this._selection;\n            for (let selectedValue of selection) {\n                if (this.compareWith(inputValue, selectedValue)) {\n                    return selectedValue;\n                }\n            }\n            return inputValue;\n        }\n    }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n    return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n\nexport { SelectionModel as S, getMultipleValuesInSingleSelectionError as g };\n\n", "export { U as UniqueSelectionDispatcher } from './unique-selection-dispatcher-Cewa_Eg3.mjs';\nexport { A as ArrayDataSource, _ as _RecycleViewRepeaterStrategy, b as _VIEW_REPEATER_STRATEGY, a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-SfuyU210.mjs';\nexport { D as DataSource, i as isDataSource } from './data-source-D34wiQZj.mjs';\nexport { _ as _DisposeViewRepeaterStrategy } from './dispose-view-repeater-strategy-Cvpav0PR.mjs';\nexport { S as SelectionModel, g as getMultipleValuesInSingleSelectionError } from './selection-model-BCgC8uEN.mjs';\nimport '@angular/core';\nimport 'rxjs';\n\n", "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input } from '@angular/core';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\nclass MatPseudoCheckbox {\n  _animationsDisabled = _animationsDisabled();\n  /** Display state of the checkbox. */\n  state = 'unchecked';\n  /** Whether the checkbox is disabled. */\n  disabled = false;\n  /**\n   * Appearance of the pseudo checkbox. Default appearance of 'full' renders a checkmark/mixedmark\n   * indicator inside a square box. 'minimal' appearance only renders the checkmark/mixedmark.\n   */\n  appearance = 'full';\n  constructor() {}\n  static ɵfac = function MatPseudoCheckbox_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatPseudoCheckbox)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatPseudoCheckbox,\n    selectors: [[\"mat-pseudo-checkbox\"]],\n    hostAttrs: [1, \"mat-pseudo-checkbox\"],\n    hostVars: 12,\n    hostBindings: function MatPseudoCheckbox_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-pseudo-checkbox-indeterminate\", ctx.state === \"indeterminate\")(\"mat-pseudo-checkbox-checked\", ctx.state === \"checked\")(\"mat-pseudo-checkbox-disabled\", ctx.disabled)(\"mat-pseudo-checkbox-minimal\", ctx.appearance === \"minimal\")(\"mat-pseudo-checkbox-full\", ctx.appearance === \"full\")(\"_mat-animation-noopable\", ctx._animationsDisabled);\n      }\n    },\n    inputs: {\n      state: \"state\",\n      disabled: \"disabled\",\n      appearance: \"appearance\"\n    },\n    decls: 0,\n    vars: 0,\n    template: function MatPseudoCheckbox_Template(rf, ctx) {},\n    styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-minimal-selected-checkmark-color, var(--mat-sys-primary))}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full{border-color:var(--mat-pseudo-checkbox-full-unselected-icon-color, var(--mat-sys-on-surface-variant));border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-pseudo-checkbox-full-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-pseudo-checkbox-full-selected-icon-color, var(--mat-sys-primary));border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-full-selected-checkmark-color, var(--mat-sys-on-primary))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-pseudo-checkbox-full-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-full-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckbox, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'mat-pseudo-checkbox',\n      template: '',\n      host: {\n        'class': 'mat-pseudo-checkbox',\n        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n        '[class.mat-pseudo-checkbox-minimal]': 'appearance === \"minimal\"',\n        '[class.mat-pseudo-checkbox-full]': 'appearance === \"full\"',\n        '[class._mat-animation-noopable]': '_animationsDisabled'\n      },\n      styles: [\".mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-minimal-selected-checkmark-color, var(--mat-sys-primary))}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full{border-color:var(--mat-pseudo-checkbox-full-unselected-icon-color, var(--mat-sys-on-surface-variant));border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-pseudo-checkbox-full-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-pseudo-checkbox-full-selected-icon-color, var(--mat-sys-primary));border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-full-selected-checkmark-color, var(--mat-sys-on-primary))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-pseudo-checkbox-full-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-full-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}\\n\"]\n    }]\n  }], () => [], {\n    state: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\nexport { MatPseudoCheckbox as M };\n", "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatPseudoCheckbox } from './pseudo-checkbox-DDmgx3P4.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nclass MatPseudoCheckboxModule {\n  static ɵfac = function MatPseudoCheckboxModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatPseudoCheckboxModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatPseudoCheckboxModule,\n    imports: [MatCommonModule, MatPseudoCheckbox],\n    exports: [MatPseudoCheckbox]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatPseudoCheckbox],\n      exports: [MatPseudoCheckbox]\n    }]\n  }], null, null);\n})();\nexport { MatPseudoCheckboxModule as M };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAAA,eAA6C;;;ACO7C,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMd,OAAO,IAAI,MAAM;AACf,aAAS,YAAY,KAAK,YAAY;AACpC,eAAS,IAAI,IAAI;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,UAAU;AACf,SAAK,WAAW,KAAK,QAAQ;AAC7B,WAAO,MAAM;AACX,WAAK,aAAa,KAAK,WAAW,OAAO,gBAAc;AACrD,eAAO,aAAa;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA2B;AAAA,EAC9D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,2BAA0B;AAAA,IACnC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACvDH,kBAAwB;AAKxB,IAAM,iBAAN,MAAqB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,aAAa,oBAAI,IAAI;AAAA;AAAA,EAErB,oBAAoB,CAAC;AAAA;AAAA,EAErB,kBAAkB,CAAC;AAAA;AAAA,EAEnB;AAAA;AAAA,EAEA,IAAI,WAAW;AACX,QAAI,CAAC,KAAK,WAAW;AACjB,WAAK,YAAY,MAAM,KAAK,KAAK,WAAW,OAAO,CAAC;AAAA,IACxD;AACA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,UAAU,IAAI,oBAAQ;AAAA,EACtB,YAAY,YAAY,OAAO,yBAAyB,eAAe,MAAM,aAAa;AACtF,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,QAAI,2BAA2B,wBAAwB,QAAQ;AAC3D,UAAI,WAAW;AACX,gCAAwB,QAAQ,WAAS,KAAK,cAAc,KAAK,CAAC;AAAA,MACtE,OACK;AACD,aAAK,cAAc,wBAAwB,CAAC,CAAC;AAAA,MACjD;AAEA,WAAK,gBAAgB,SAAS;AAAA,IAClC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,QAAQ;AACd,SAAK,uBAAuB,MAAM;AAClC,WAAO,QAAQ,WAAS,KAAK,cAAc,KAAK,CAAC;AACjD,UAAM,UAAU,KAAK,kBAAkB;AACvC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,QAAQ;AAChB,SAAK,uBAAuB,MAAM;AAClC,WAAO,QAAQ,WAAS,KAAK,gBAAgB,KAAK,CAAC;AACnD,UAAM,UAAU,KAAK,kBAAkB;AACvC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,QAAQ;AACpB,SAAK,uBAAuB,MAAM;AAClC,UAAM,YAAY,KAAK;AACvB,UAAM,iBAAiB,IAAI,IAAI,OAAO,IAAI,WAAS,KAAK,kBAAkB,KAAK,CAAC,CAAC;AACjF,WAAO,QAAQ,WAAS,KAAK,cAAc,KAAK,CAAC;AACjD,cACK,OAAO,WAAS,CAAC,eAAe,IAAI,KAAK,kBAAkB,OAAO,cAAc,CAAC,CAAC,EAClF,QAAQ,WAAS,KAAK,gBAAgB,KAAK,CAAC;AACjD,UAAM,UAAU,KAAK,kBAAkB;AACvC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO;AACV,WAAO,KAAK,WAAW,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,OAAO,KAAK;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,aAAa,MAAM;AACrB,SAAK,WAAW;AAChB,UAAM,UAAU,KAAK,kBAAkB;AACvC,QAAI,YAAY;AACZ,WAAK,iBAAiB;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,WAAO,KAAK,WAAW,IAAI,KAAK,kBAAkB,KAAK,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACN,WAAO,KAAK,WAAW,SAAS;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,CAAC,KAAK,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,WAAW;AACZ,QAAI,KAAK,aAAa,KAAK,UAAU;AACjC,WAAK,UAAU,KAAK,SAAS;AAAA,IACjC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AAClB,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,mBAAmB;AAEf,SAAK,YAAY;AACjB,QAAI,KAAK,gBAAgB,UAAU,KAAK,kBAAkB,QAAQ;AAC9D,WAAK,QAAQ,KAAK;AAAA,QACd,QAAQ;AAAA,QACR,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,MAClB,CAAC;AACD,WAAK,oBAAoB,CAAC;AAC1B,WAAK,kBAAkB,CAAC;AAAA,IAC5B;AAAA,EACJ;AAAA;AAAA,EAEA,cAAc,OAAO;AACjB,YAAQ,KAAK,kBAAkB,KAAK;AACpC,QAAI,CAAC,KAAK,WAAW,KAAK,GAAG;AACzB,UAAI,CAAC,KAAK,WAAW;AACjB,aAAK,WAAW;AAAA,MACpB;AACA,UAAI,CAAC,KAAK,WAAW,KAAK,GAAG;AACzB,aAAK,WAAW,IAAI,KAAK;AAAA,MAC7B;AACA,UAAI,KAAK,cAAc;AACnB,aAAK,gBAAgB,KAAK,KAAK;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACnB,YAAQ,KAAK,kBAAkB,KAAK;AACpC,QAAI,KAAK,WAAW,KAAK,GAAG;AACxB,WAAK,WAAW,OAAO,KAAK;AAC5B,UAAI,KAAK,cAAc;AACnB,aAAK,kBAAkB,KAAK,KAAK;AAAA,MACrC;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAEA,aAAa;AACT,QAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAK,WAAW,QAAQ,WAAS,KAAK,gBAAgB,KAAK,CAAC;AAAA,IAChE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,QAAQ;AAC3B,QAAI,OAAO,SAAS,KAAK,CAAC,KAAK,cAAc,OAAO,cAAc,eAAe,YAAY;AACzF,YAAM,wCAAwC;AAAA,IAClD;AAAA,EACJ;AAAA;AAAA,EAEA,oBAAoB;AAChB,WAAO,CAAC,EAAE,KAAK,kBAAkB,UAAU,KAAK,gBAAgB;AAAA,EACpE;AAAA;AAAA,EAEA,kBAAkB,YAAY,WAAW;AACrC,QAAI,CAAC,KAAK,aAAa;AACnB,aAAO;AAAA,IACX,OACK;AACD,kBAAY,aAAa,KAAK;AAC9B,eAAS,iBAAiB,WAAW;AACjC,YAAI,KAAK,YAAY,YAAY,aAAa,GAAG;AAC7C,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAMA,SAAS,0CAA0C;AAC/C,SAAO,MAAM,yEAAyE;AAC1F;;;ACnNA,IAAAC,eAAO;;;AHyIP,uBAA0B;;;AI9H1B,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,sBAAsB,oBAAoB;AAAA;AAAA,EAE1C,QAAQ;AAAA;AAAA,EAER,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,aAAa;AAAA,EACb,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,IACnC,WAAW,CAAC,GAAG,qBAAqB;AAAA,IACpC,UAAU;AAAA,IACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,qCAAqC,IAAI,UAAU,eAAe,EAAE,+BAA+B,IAAI,UAAU,SAAS,EAAE,gCAAgC,IAAI,QAAQ,EAAE,+BAA+B,IAAI,eAAe,SAAS,EAAE,4BAA4B,IAAI,eAAe,MAAM,EAAE,2BAA2B,IAAI,mBAAmB;AAAA,MACjW;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,MACV,YAAY;AAAA,IACd;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,2BAA2B,IAAI,KAAK;AAAA,IAAC;AAAA,IACxD,QAAQ,CAAC,+xGAAiyG;AAAA,IAC1yG,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,6CAA6C;AAAA,QAC7C,uCAAuC;AAAA,QACvC,wCAAwC;AAAA,QACxC,uCAAuC;AAAA,QACvC,oCAAoC;AAAA,QACpC,mCAAmC;AAAA,MACrC;AAAA,MACA,QAAQ,CAAC,+xGAAiyG;AAAA,IAC5yG,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;ACjFH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,iBAAiB;AAAA,IAC5C,SAAS,CAAC,iBAAiB;AAAA,EAC7B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB;AAAA,MAC5C,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ALbH,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM;AACZ,IAAM,MAAM,CAAC,iBAAiB;AAC9B,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,mBAAmB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC;AAC/L,IAAM,MAAM,CAAC,yCAAyC,sBAAsB,qBAAqB,KAAK,qBAAqB,aAAa;AACxI,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,mBAAmB,EAAE,CAAC,CAAC;AAChK,IAAM,MAAM,CAAC,sBAAsB,qBAAqB,KAAK,eAAe,uCAAuC;AACnH,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,SAAS,EAAE;AAC3B,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa;AAChB,IAAG,gBAAgB;AACnB,IAAG,UAAU,GAAG,OAAO,EAAE;AACzB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,0BAA0B,OAAO,QAAQ;AACxD,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,QAAQ,EAAE,YAAY,OAAO,QAAQ;AAAA,EACvE;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,SAAS,EAAE;AAC3B,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,UAAU,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE;AACvC,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,uBAAuB,OAAO,QAAQ;AACrD,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,QAAQ,EAAE,YAAY,OAAO,QAAQ;AAAA,EACvE;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AAAC;AACtE,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,eAAe,CAAC;AAC3F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,cAAiB,YAAY,CAAC;AACpC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,WAAW;AAAA,EAC/C;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AAAC;AACtE,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,eAAe,CAAC;AAC3F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,WAAc,YAAY,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,QAAQ;AAAA,EAC5C;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AAAC;AACtE,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,eAAe,CAAC;AAAA,EAC7F;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,WAAc,YAAY,CAAC;AACjC,IAAG,WAAW,oBAAoB,QAAQ;AAAA,EAC5C;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AAAC;AACvE,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,eAAe,CAAC;AAC5F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,cAAiB,YAAY,CAAC;AACpC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,WAAW;AAAA,EAC/C;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AAAC;AACvE,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,eAAe,CAAC;AAC5F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,WAAc,YAAY,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,oBAAoB,QAAQ;AAAA,EAC5C;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AAAC;AACvE,SAAS,sCAAsC,IAAI,KAAK;AACtD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,eAAe,CAAC;AAAA,EAC9F;AACA,MAAI,KAAK,GAAG;AACV,IAAG,cAAc;AACjB,UAAM,WAAc,YAAY,CAAC;AACjC,IAAG,WAAW,oBAAoB,QAAQ;AAAA,EAC5C;AACF;AAoBA,IAAM,cAAc,IAAI,eAAe,YAAY;AAQnD,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc,OAAO,UAAU;AAAA,EAC/B,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IACxC,WAAW,CAAC,GAAG,2BAA2B,6BAA6B;AAAA,EACzE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAOH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc,OAAO,UAAU;AAAA,EAC/B,cAAc;AAAA,EAAC;AAAA,EACf,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,WAAW,CAAC,GAAG,0BAA0B,+BAA+B;AAAA,EAC1E,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAOH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,WAAW,CAAC,GAAG,0BAA0B,oBAAoB;AAAA,EAC/D,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AASH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,cAAc,OAAO,aAAa;AAAA,IAChC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,cAAc;AAAA,EAAC;AAAA,EACf,oBAAoB;AAGlB,WAAO,CAAC,KAAK,eAAe,KAAK,aAAa,mBAAmB,MAAM;AAAA,EACzE;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,UAAU;AAAA,IACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,wBAAwB,IAAI,kBAAkB,CAAC,EAAE,sBAAsB,CAAC,IAAI,kBAAkB,CAAC;AAAA,MAChH;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,QAKJ,gCAAgC;AAAA,QAChC,8BAA8B;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,oBAAN,MAAM,2BAA0B,wBAAwB;AAAA,EACtD,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,0BAA0B,mBAAmB;AAC3D,cAAQ,mCAAmC,iCAAoC,sBAAsB,kBAAiB,IAAI,qBAAqB,kBAAiB;AAAA,IAClK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,IACzC,WAAW,CAAC,GAAG,0BAA0B;AAAA,IACzC,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,kBAAN,MAAM,yBAAwB,wBAAwB;AAAA,EACpD,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,wBAAwB,mBAAmB;AACzD,cAAQ,iCAAiC,+BAAkC,sBAAsB,gBAAe,IAAI,qBAAqB,gBAAe;AAAA,IAC1J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,WAAW,CAAC,GAAG,wBAAwB;AAAA,IACvC,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,kBAAkB,IAAI,eAAe,iBAAiB;AAG5D,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,oBAAoB;AAAA;AAAA,EAEpB,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,iBAAiB,sBAAsB,KAAK;AAAA,EACnD;AAAA,EACA,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,IAAI,WAAW;AACb,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,UAAU,IAAI,sBAAsB,KAAK,CAAC;AAAA,EACjD;AAAA,EACA,YAAY,OAAO,KAAK;AAAA,EACxB,kBAAkB,OAAO,iBAAiB;AAAA,IACxC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,IAAI,QAAQ;AAAA,MAC9C;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,eAAe;AAAA,MACf,UAAU;AAAA,IACZ;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,QACJ,wBAAwB;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc,OAAO,UAAU;AAAA,EAC/B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,aAAa;AAAA,IAC9B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,QAAQ;AAAA;AAAA,EAE3B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,kBAAkB,oBAAoB;AAAA,EACtC;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,IAAI,MAAM,OAAO;AACf,SAAK,iBAAiB,qBAAqB,OAAO,IAAI;AACtD,SAAK,iBAAiB,KAAK;AAAA,EAC7B;AAAA,EACA,iBAAiB;AAAA;AAAA,EAEjB,IAAI,gBAAgB;AAClB,WAAO,KAAK,YAAY,KAAK,kBAAkB,KAAK,mBAAmB,CAAC,CAAC,KAAK,WAAW;AAAA,EAC3F;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,iBAAiB,sBAAsB,KAAK;AAAA,EACnD;AAAA,EACA,iBAAiB;AAAA;AAAA,EAEjB,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,KAAK,CAAC,CAAC,KAAK,WAAW;AAAA,EAC/C;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,UAAU,IAAI,sBAAsB,KAAK,CAAC;AAAA,EACjD;AAAA,EACA,YAAY,OAAO,KAAK;AAAA,EACxB,iBAAiB,IAAI,0BAAa;AAAA,EAClC,kBAAkB;AAAA;AAAA,EAElB,0BAA0B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,iBAAiB;AACnB,WAAO,KAAK,iBAAiB,CAAC,CAAC,KAAK,aAAa;AAAA,EACnD;AAAA,EACA,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,uBAAuB;AAC3D,UAAM,sBAAsB,OAAO,2BAA2B;AAAA,MAC5D,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,eAAe,uBAAuB,CAAC;AAC5C,SAAK,eAAe,KAAK,YAAY;AACrC,SAAK,mBAAmB,KAAK,aAAa,SAAS,YAAY,MAAM;AACrE,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU,mBAAmB;AACvD,WAAK,yBAAyB;AAAA,IAChC;AAIA,QAAI,KAAK,oBAAoB,CAAC,KAAK,aAAa,aAAa,MAAM,GAAG;AACpE,WAAK,aAAa,aAAa,QAAQ,QAAQ;AAAA,IACjD;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,+BAA+B;AACpC,SAAK,iBAAiB,IAAI;AAAA,EAC5B;AAAA,EACA,cAAc;AACZ,SAAK,eAAe,YAAY;AAChC,QAAI,KAAK,oBAAoB,MAAM;AACjC,WAAK,gBAAgB,qBAAqB;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AACjB,WAAO,CAAC,EAAE,KAAK,SAAS,UAAU,KAAK,OAAO;AAAA,EAChD;AAAA,EACA,2BAA2B;AACzB,SAAK,aAAa,UAAU,IAAI,+BAA+B;AAC/D,SAAK,kBAAkB,IAAI,eAAe,MAAM,KAAK,SAAS,KAAK,cAAc,KAAK,WAAW,OAAO,QAAQ,CAAC;AACjH,SAAK,gBAAgB,mBAAmB,KAAK,YAAY;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iCAAiC;AAC/B,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,eAAe,QAAI,oBAAM,KAAK,OAAO,SAAS,KAAK,QAAQ,OAAO,EAAE,UAAU,MAAM,KAAK,iBAAiB,KAAK,CAAC,CAAC;AAAA,IACxH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,iBAAiB,wBAAwB;AAGvC,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,WAAW,CAAC,KAAK,kBAAkB;AAC3D;AAAA,IACF;AAIA,QAAI,wBAAwB;AAC1B,WAAK,gCAAgC;AAAA,IACvC;AAGA,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,iCAA2B,IAAI;AAAA,IACjC;AACA,UAAM,gBAAgB,KAAK,kBAAkB,KAAK,uBAAuB;AACzE,UAAM,oBAAoB,KAAK,iBAAiB;AAEhD,SAAK,aAAa,UAAU,OAAO,iCAAiC,iBAAiB,CAAC;AACtF,SAAK,aAAa,UAAU,OAAO,gCAAgC,iBAAiB,CAAC;AACrF,SAAK,aAAa,UAAU,OAAO,iCAAiC,kBAAkB,CAAC;AACvF,SAAK,aAAa,UAAU,OAAO,mCAAmC,kBAAkB,CAAC;AAGzF,QAAI,KAAK,yBAAyB;AAChC,YAAM,eAAe,KAAK,QAAQ,WAAW,KAAK,kBAAkB;AACpE,wBAAkB,UAAU,OAAO,+BAA+B,YAAY;AAC9E,wBAAkB,UAAU,OAAO,iCAAiC,CAAC,YAAY;AAAA,IACnF,OAAO;AACL,wBAAkB,UAAU,OAAO,6BAA6B;AAChE,wBAAkB,UAAU,OAAO,+BAA+B;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,yBAAyB;AACvB,QAAI,aAAa,KAAK,QAAQ,SAAS,KAAK,OAAO;AACnD,QAAI,KAAK,yBAAyB;AAChC,oBAAc;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kCAAkC;AAChC,SAAK,0BAA0B,MAAM,KAAK,KAAK,iBAAiB,cAAc,UAAU,EAAE,OAAO,UAAQ,KAAK,aAAa,KAAK,YAAY,EAAE,KAAK,UAAQ,CAAC,EAAE,KAAK,eAAe,KAAK,YAAY,KAAK,EAAE;AAAA,EAC5M;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,gBAAgB,SAAS,+BAA+B,IAAI,KAAK,UAAU;AACzE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,mBAAmB,CAAC;AAChD,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAAA,MAChD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW;AAC5D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,UAAU;AAAA,IACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,IAAI,QAAQ,EAAE,YAAY,IAAI,oBAAoB,IAAI,YAAY,IAAI;AACtG,QAAG,YAAY,2BAA2B,IAAI,QAAQ;AAAA,MACxD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,eAAe;AAAA,MACf,UAAU;AAAA,IACZ;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,QACJ,mCAAmC;AAAA,QACnC,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,QACxB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,SAAS,2BAA2B,MAAM;AACxC,QAAM,YAAY,KAAK,QAAQ;AAC/B,QAAM,WAAW,KAAK,OAAO;AAC7B,MAAI,YAAY,GAAG;AACjB,YAAQ,KAAK,0CAA0C;AAAA,EACzD;AACA,MAAI,cAAc,KAAK,WAAW,GAAG;AACnC,YAAQ,KAAK,kEAAkE;AAAA,EACjF;AACA,MAAI,cAAc,KAAK,KAAK,2BAA2B,KAAK,mBAAmB,QAAQ,KAAK,iBAAiB,GAAG;AAC9G,YAAQ,KAAK,2DAA2D;AAAA,EAC1E;AACA,MAAI,WAAW,KAAK,aAAa,KAAK,KAAK,yBAAyB;AAClE,YAAQ,KAAK,8CAA8C;AAAA,EAC7D;AACF;AACA,IAAM,gBAAN,MAAM,uBAAsB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtC,oBAAoB;AAAA,EACpB,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,IAC/B,WAAW,CAAC,QAAQ,SAAS,GAAG,uBAAuB,qBAAqB,UAAU;AAAA,IACtF,UAAU,CAAC,eAAe;AAAA,IAC1B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,+kjBAAqmjB;AAAA,IAC9mjB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,QAAQ,CAAC,+kjBAAqmjB;AAAA,IAChnjB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,WAAW,IAAI,eAAe,SAAS;AAC7C,IAAM,UAAN,MAAM,iBAAgB,YAAY;AAAA,EAChC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,gBAAgB,mBAAmB;AACjD,cAAQ,yBAAyB,uBAA0B,sBAAsB,QAAO,IAAI,qBAAqB,QAAO;AAAA,IAC1H;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,IACxB,WAAW,CAAC,GAAG,gBAAgB,qBAAqB,UAAU;AAAA,IAC9D,UAAU,CAAC,SAAS;AAAA,IACpB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,QAAQ,CAAC,+kjBAAqmjB;AAAA,IAChnjB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,cAAN,MAAM,qBAAoB,gBAAgB;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,WAAW;AACvB,SAAK,aAAa,sBAAsB,SAAS;AAAA,EACnD;AAAA,EACA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,kBAAkB;AAChB,WAAO,KAAK,aAAa,aAAa,OAAO,KAAK,aAAa,SAAS;AAAA,EAC1E;AAAA,EACA,6BAA6B;AAC3B,WAAO,KAAK,MAAM,WAAW,MAAM,KAAK,SAAS,WAAW,KAAK,KAAK,OAAO,WAAW;AAAA,EAC1F;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,oBAAoB,mBAAmB;AACrD,cAAQ,6BAA6B,2BAA8B,sBAAsB,YAAW,IAAI,qBAAqB,YAAW;AAAA,IAC1I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,eAAe,GAAG,CAAC,KAAK,iBAAiB,EAAE,GAAG,CAAC,UAAU,iBAAiB,EAAE,CAAC;AAAA,IAC1F,gBAAgB,SAAS,2BAA2B,IAAI,KAAK,UAAU;AACrE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAC9C,QAAG,eAAe,UAAU,kBAAkB,CAAC;AAC/C,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAAA,MAChD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS;AAC1D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU;AAC3D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ;AAAA,MAC3D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AACvE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY,GAAG;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,qBAAqB,eAAe;AAAA,IACnD,UAAU;AAAA,IACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gBAAgB,IAAI,gBAAgB,CAAC;AACpD,QAAG,YAAY,4BAA4B,IAAI,SAAS,EAAE,sCAAsC,IAAI,SAAS,WAAW,CAAC,EAAE,oCAAoC,IAAI,OAAO,WAAW,CAAC,EAAE,qCAAqC,IAAI,MAAM,WAAW,CAAC,EAAE,+CAA+C,IAAI,2BAA2B,CAAC,EAAE,2BAA2B,IAAI,eAAe;AAAA,MACtX;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW;AAAA,IACb;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,IACxB,UAAU,CAAI,0BAA0B;AAAA,IACxC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,sCAAsC,GAAG,mBAAmB,GAAG,CAAC,GAAG,qBAAqB,CAAC;AAAA,IAC9J,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB,GAAG;AACtB,QAAG,aAAa,CAAC;AACjB,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,eAAe,GAAG,QAAQ,GAAG,CAAC;AACjC,QAAG,WAAW,qBAAqB,SAAS,yDAAyD;AACnG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,iBAAiB,IAAI,CAAC;AAAA,QAClD,CAAC;AACD,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,aAAa,EAAE;AAClB,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,aAAa,GAAG,CAAC;AACpB,QAAG,UAAU,GAAG,OAAO,CAAC;AAAA,MAC1B;AAAA,IACF;AAAA,IACA,cAAc,CAAC,iBAAiB;AAAA,IAChC,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,oCAAoC;AAAA,QACpC,8CAA8C;AAAA,QAC9C,4CAA4C;AAAA,QAC5C,6CAA6C;AAAA;AAAA;AAAA,QAG7C,uDAAuD;AAAA,QACvD,mCAAmC;AAAA,QACnC,uBAAuB;AAAA,MACzB;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,iBAAiB;AAAA,MAC3B,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,iBAAiB,IAAI,eAAe,eAAe;AACzD,IAAM,gBAAN,MAAM,uBAAsB,gBAAgB;AAAA,EAC1C,iBAAiB,OAAO,cAAc;AAAA,EACtC,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,IAAI,aAAa;AAAA;AAAA,EAElC,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,IAAI,mBAAmB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,iBAAiB,OAAO;AAC1B,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK,eAAe;AAAA,EAC5C;AAAA,EACA,IAAI,MAAM,UAAU;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,UAAU;AAClB,QAAI,KAAK,YAAY,aAAa,KAAK,SAAS,KAAK,oBAAoB;AACvE,WAAK,WAAW;AAAA,IAClB;AACA,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,eAAe,gBAAgB,WAAW,IAAI;AAAA,EAC5D;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,UAAM,aAAa,sBAAsB,KAAK;AAC9C,QAAI,eAAe,KAAK,WAAW;AACjC,WAAK,aAAa,UAAU;AAC5B,UAAI,cAAc,KAAK,eAAe,UAAU;AAC9C,aAAK,eAAe,mBAAmB;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,qBAAqB;AAAA,EACrB,WAAW;AACT,UAAM,OAAO,KAAK;AAClB,QAAI,KAAK,UAAU,KAAK,OAAO,KAAK,WAAS,KAAK,YAAY,KAAK,QAAQ,KAAK,CAAC,GAAG;AAClF,WAAK,aAAa,IAAI;AAAA,IACxB;AACA,UAAM,cAAc,KAAK;AAMzB,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,UAAI,KAAK,aAAa,aAAa;AACjC,aAAK,WAAW;AAChB,aAAK,mBAAmB,aAAa;AAAA,MACvC;AAAA,IACF,CAAC;AACD,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,QAAI,KAAK,UAAU;AAGjB,cAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,aAAK,WAAW;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,WAAW,CAAC,KAAK;AAAA,EACxB;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,aAAa,MAAM;AAAA,EAC1B;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,eAAe,KAAK,SAAS,IAAI,CAAC,GAAG,YAAY;AAGvD,UAAM,UAAU,gBAAgB,KAAK,kBAAkB;AACvD,WAAO,SAAS,eAAe;AAAA,EACjC;AAAA;AAAA,EAEA,eAAe,UAAU;AACvB,WAAO,KAAK,eAAe,YAAY,KAAK,mBAAmB,MAAM;AAAA,EACvE;AAAA;AAAA,EAEA,YAAY,UAAU;AACpB,WAAO,CAAC,KAAK,eAAe,YAAY,KAAK,mBAAmB,MAAM,YAAY,CAAC,KAAK,eAAe;AAAA,EACzG;AAAA;AAAA,EAEA,qBAAqB,UAAU;AAC7B,WAAO,KAAK,cAAc,SAAS,QAAQ,KAAK,KAAK,cAAc,WAAW,QAAQ;AAAA,EACxF;AAAA;AAAA,EAEA,cAAc,MAAM,UAAU;AAG5B,WAAO,KAAK,mBAAmB,MAAM,aAAa,SAAS,YAAY,KAAK,SAAS,WAAW,IAAI,KAAK,OAAO,WAAW;AAAA,EAC7H;AAAA,EACA,cAAc;AACZ,SAAK,eAAe,WAAW;AAAA,EACjC;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK,kBAAkB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,UAAU;AACrB,QAAI,aAAa,KAAK,WAAW;AAC/B,aAAO;AAAA,IACT;AACA,SAAK,YAAY;AACjB,QAAI,UAAU;AACZ,WAAK,eAAe,gBAAgB,OAAO,IAAI;AAAA,IACjD,OAAO;AACL,WAAK,eAAe,gBAAgB,SAAS,IAAI;AAAA,IACnD;AACA,SAAK,eAAe,KAAK,QAAQ;AACjC,SAAK,mBAAmB,aAAa;AACrC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AACd,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,uBAAuB;AACrB,QAAI,CAAC,KAAK,UAAU;AAClB,UAAI,KAAK,eAAe,UAAU;AAChC,aAAK,WAAW,CAAC,KAAK;AACtB,aAAK,eAAe,iBAAiB,CAAC,IAAI,CAAC;AAAA,MAC7C,WAAW,CAAC,KAAK,UAAU;AACzB,aAAK,WAAW;AAChB,aAAK,eAAe,iBAAiB,CAAC,IAAI,CAAC;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,SAAK,aAAa,aAAa,YAAY,QAAQ,EAAE;AAAA,EACvD;AAAA,EACA,6BAA6B;AAC3B,UAAM,aAAa,KAAK,cAAc,WAAW,QAAQ,KAAK,KAAK,cAAc,SAAS,QAAQ,KAAK,KAAK,eAAe,QAAQ,KAAK,KAAK,YAAY,QAAQ;AACjK,UAAM,cAAc,KAAK,cAAc,SAAS,OAAO,KAAK,KAAK,cAAc,WAAW,OAAO,KAAK,KAAK,eAAe,OAAO,KAAK,KAAK,YAAY,OAAO;AAC9J,WAAO,cAAc;AAAA,EACvB;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,sBAAsB,mBAAmB;AACvD,cAAQ,+BAA+B,6BAAgC,sBAAsB,cAAa,IAAI,qBAAqB,cAAa;AAAA,IAClJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,IAC/B,gBAAgB,SAAS,6BAA6B,IAAI,KAAK,UAAU;AACvE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,iBAAiB,CAAC;AAC9C,QAAG,eAAe,UAAU,kBAAkB,CAAC;AAAA,MACjD;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS;AAC1D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU;AAAA,MAC7D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,oBAAoB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,MACzE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,QAAQ,UAAU,GAAG,qBAAqB,uBAAuB,eAAe;AAAA,IAC5F,UAAU;AAAA,IACV,cAAc,SAAS,2BAA2B,IAAI,KAAK;AACzD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,QAAQ,SAAS,wCAAwC;AACrE,iBAAO,IAAI,YAAY;AAAA,QACzB,CAAC,EAAE,SAAS,SAAS,yCAAyC;AAC5D,iBAAO,IAAI,qBAAqB;AAAA,QAClC,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,iBAAiB,IAAI,QAAQ;AAC5C,QAAG,YAAY,2BAA2B,IAAI,YAAY,CAAC,IAAI,eAAe,YAAY,IAAI,eAAe,4BAA4B,EAAE,sCAAsC,IAAI,cAAc,WAAW,QAAQ,CAAC,EAAE,oCAAoC,IAAI,cAAc,SAAS,QAAQ,CAAC,EAAE,qCAAqC,IAAI,cAAc,SAAS,OAAO,CAAC,EAAE,4CAA4C,IAAI,cAAc,WAAW,OAAO,CAAC,EAAE,wCAAwC,IAAI,eAAe,QAAQ,CAAC,EAAE,yCAAyC,IAAI,eAAe,OAAO,CAAC,EAAE,qCAAqC,IAAI,YAAY,QAAQ,CAAC,EAAE,sCAAsC,IAAI,YAAY,OAAO,CAAC,EAAE,+CAA+C,IAAI,2BAA2B,CAAC,EAAE,cAAc,IAAI,UAAU,aAAa,IAAI,UAAU,MAAM,EAAE,YAAY,IAAI,UAAU,MAAM,EAAE,2BAA2B,IAAI,eAAe;AAAA,MAC96B;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,eAAe;AAAA,IAC1B,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,wBAAwB,qCAAqC,GAAG,CAAC,GAAG,wBAAwB,kCAAkC,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,sCAAsC,GAAG,mBAAmB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,QAAQ,YAAY,GAAG,gCAAgC,GAAG,WAAW,UAAU,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,WAAW,aAAa,eAAe,QAAQ,GAAG,yBAAyB,GAAG,CAAC,QAAQ,QAAQ,KAAK,oCAAoC,GAAG,8BAA8B,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,QAAQ,SAAS,GAAG,6BAA6B,GAAG,WAAW,UAAU,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,yBAAyB,CAAC;AAAA,IAC15B,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB,GAAG;AACtB,QAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,sCAAsC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB,EAAE,GAAG,sCAAsC,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAClT,QAAG,oBAAoB,GAAG,sCAAsC,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,sCAAsC,GAAG,GAAG,QAAQ,CAAC;AACzI,QAAG,oBAAoB,GAAG,sCAAsC,GAAG,GAAG,MAAM,CAAC;AAC7E,QAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,QAAG,aAAa,EAAE;AAClB,QAAG,aAAa,IAAI,CAAC;AACrB,QAAG,eAAe,IAAI,QAAQ,GAAG,CAAC;AAClC,QAAG,WAAW,qBAAqB,SAAS,4DAA4D;AACtG,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,iBAAiB,IAAI,CAAC;AAAA,QAClD,CAAC;AACD,QAAG,aAAa,IAAI,CAAC;AACrB,QAAG,aAAa,EAAE;AAClB,QAAG,oBAAoB,IAAI,uCAAuC,GAAG,GAAG,QAAQ,CAAC,EAAE,IAAI,uCAAuC,GAAG,GAAG,QAAQ,CAAC;AAC7I,QAAG,oBAAoB,IAAI,uCAAuC,GAAG,GAAG,MAAM,CAAC;AAC/E,QAAG,aAAa,IAAI,CAAC;AACrB,QAAG,UAAU,IAAI,OAAO,EAAE;AAAA,MAC5B;AACA,UAAI,KAAK,GAAG;AACV,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,eAAe,QAAQ,IAAI,IAAI,IAAI,YAAY,QAAQ,IAAI,IAAI,EAAE;AACtF,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,qBAAqB,QAAQ,IAAI,IAAI,EAAE;AAC5D,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,eAAe,OAAO,IAAI,KAAK,IAAI,YAAY,OAAO,IAAI,KAAK,EAAE;AACtF,QAAG,UAAU,CAAC;AACd,QAAG,cAAc,IAAI,qBAAqB,OAAO,IAAI,KAAK,EAAE;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,cAAc,CAAC,kBAAkB,iBAAiB;AAAA,IAClD,QAAQ,CAAC,65kBAAq6kB;AAAA,IAC96kB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA;AAAA;AAAA,QAGR,mCAAmC;AAAA;AAAA;AAAA,QAGnC,8CAA8C;AAAA,QAC9C,4CAA4C;AAAA,QAC5C,6CAA6C;AAAA,QAC7C,oDAAoD;AAAA;AAAA;AAAA,QAGpD,gDAAgD;AAAA,QAChD,iDAAiD;AAAA,QACjD,6CAA6C;AAAA,QAC7C,8CAA8C;AAAA;AAAA;AAAA,QAG9C,uDAAuD;AAAA,QACvD,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,mCAAmC;AAAA,QACnC,wBAAwB;AAAA,QACxB,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,kBAAkB,iBAAiB;AAAA,MAC7C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,65kBAAq6kB;AAAA,IACh7kB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,OAAO,OAAO,SAAS,qCAAqC,mBAAmB;AAC7E,WAAO,KAAK,qBAAqB,+BAA8B;AAAA,EACjE;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IAC/D,WAAW,CAAC,GAAG,qBAAqB,2BAA2B;AAAA,EACjE,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA;AAAA;AAAA,MAGV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAOH,IAAM,eAAe,IAAI,eAAe,YAAY;AACpD,IAAM,aAAN,MAAM,oBAAmB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,oBAAoB;AAAA,EACpB,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,mBAAmB,mBAAmB;AACpD,cAAQ,4BAA4B,0BAA6B,sBAAsB,WAAU,IAAI,qBAAqB,WAAU;AAAA,IACtI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,QAAQ,cAAc,GAAG,oBAAoB,qBAAqB,UAAU;AAAA,IACxF,UAAU,CAAC,YAAY;AAAA,IACvB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,0BAA0B;AAAA,IAClC,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,QAAQ,CAAC,+kjBAAqmjB;AAAA,IAChnjB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oCAAoC;AAAA,EACxC,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,gBAAgB;AAAA,EAC9C,OAAO;AACT;AAEA,IAAM,yBAAN,MAA6B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,YACA,QACA,SAAS;AACP,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACjB;AACF;AACA,IAAM,mBAAN,MAAM,0BAAyB,YAAY;AAAA,EACzC,WAAW,OAAO,UAAU;AAAA,EAC5B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,SAAS;AAAA,EAC5B,eAAe;AAAA,EACf;AAAA,EACA;AAAA;AAAA,EAEA,aAAa,IAAI,qBAAQ;AAAA;AAAA,EAEzB;AAAA;AAAA,EAEA,YAAY,OAAK;AAAA,EAAC;AAAA,EAClB;AAAA;AAAA,EAEA,kBAAkB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASnC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,cAAc,CAAC,IAAI,OAAO,OAAO;AAAA;AAAA,EAEjC,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,UAAM,WAAW,sBAAsB,KAAK;AAC5C,QAAI,aAAa,KAAK,WAAW;AAC/B,WAAK,OAAO,cAAc,eAAe,cAAc,KAAK,cAAc;AACxE,cAAM,IAAI,MAAM,2EAA2E;AAAA,MAC7F;AACA,WAAK,YAAY;AACjB,WAAK,kBAAkB,IAAI,eAAe,KAAK,WAAW,KAAK,gBAAgB,QAAQ;AAAA,IACzF;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,+BAA+B;AACjC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,6BAA6B,OAAO;AACtC,SAAK,gCAAgC,sBAAsB,KAAK;AAAA,EAClE;AAAA,EACA,gCAAgC,KAAK,iBAAiB,gCAAgC;AAAA;AAAA,EAEtF,kBAAkB,IAAI,eAAe,KAAK,SAAS;AAAA;AAAA,EAEnD;AAAA;AAAA,EAEA,aAAa,MAAM;AAAA,EAAC;AAAA,EACpB,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc;AACZ,UAAM;AACN,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,kBAAkB;AAGhB,SAAK,eAAe;AACpB,SAAK,qBAAqB;AAG1B,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,oBAAoB,CAAC,KAAK,UAAU,OAAO,KAAK,SAAS,eAAe,WAAW,KAAK,cAAc,GAAG,KAAK,UAAU,OAAO,KAAK,SAAS,eAAe,YAAY,KAAK,eAAe,CAAC;AAAA,IACpM,CAAC;AACD,QAAI,KAAK,QAAQ;AACf,WAAK,sBAAsB,KAAK,MAAM;AAAA,IACxC;AACA,SAAK,yBAAyB;AAAA,EAChC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,kBAAkB,QAAQ,UAAU;AAC1C,UAAM,uBAAuB,QAAQ,eAAe;AACpD,UAAM,sCAAsC,QAAQ,8BAA8B;AAClF,QAAI,wBAAwB,CAAC,qBAAqB,eAAe,mBAAmB,CAAC,gBAAgB,eAAe,uCAAuC,CAAC,oCAAoC,aAAa;AAC3M,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,QAAQ;AAC1B,SAAK,mBAAmB,QAAQ,aAAW,QAAQ,CAAC;AACpD,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA,EAEA,MAAM,SAAS;AACb,SAAK,SAAS,cAAc,MAAM,OAAO;AAAA,EAC3C;AAAA;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,uBAAuB,IAAI;AAAA,EACzC;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,uBAAuB,KAAK;AAAA,EAC1C;AAAA;AAAA,EAEA,qBAAqB;AAInB,QAAI,KAAK,WAAW,CAAC,KAAK,cAAc;AACtC,YAAM,QAAQ,KAAK,yBAAyB;AAC5C,WAAK,UAAU,KAAK;AACpB,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,SAAS;AACxB,SAAK,gBAAgB,KAAK,IAAI,uBAAuB,MAAM,OAAO,CAAC;AAAA,EACrE;AAAA;AAAA,EAEA,WAAW,QAAQ;AACjB,SAAK,SAAS;AACd,QAAI,KAAK,SAAS;AAChB,WAAK,sBAAsB,UAAU,CAAC,CAAC;AAAA,IACzC;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAChB,SAAK,mBAAmB,aAAa;AACrC,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK,uBAAuB;AAAA,EACrC;AAAA,EACA,IAAI,SAAS,OAAO;AAIlB,SAAK,uBAAuB,IAAI,sBAAsB,KAAK,CAAC;AAC5D,QAAI,KAAK,uBAAuB,GAAG;AACjC,WAAK,aAAa,cAAc,EAAE;AAAA,IACpC;AAAA,EACF;AAAA,EACA,yBAAyB,OAAO,KAAK;AAAA;AAAA,EAErC,iBAAiB,IAAI;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,2BAA2B;AACzB,SAAK,gBAAgB,QAAQ,SAAK,4BAAU,KAAK,UAAU,CAAC,EAAE,UAAU,WAAS;AAE/E,eAAS,QAAQ,MAAM,OAAO;AAC5B,aAAK,WAAW;AAAA,MAClB;AACA,eAAS,QAAQ,MAAM,SAAS;AAC9B,aAAK,WAAW;AAAA,MAClB;AACA,UAAI,CAAC,KAAK,eAAe,GAAG;AAC1B,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB,QAAQ;AAC5B,SAAK,QAAQ,QAAQ,YAAU,OAAO,aAAa,KAAK,CAAC;AACzD,WAAO,QAAQ,WAAS;AACtB,YAAM,sBAAsB,KAAK,QAAQ,KAAK,YAAU;AAGtD,eAAO,OAAO,WAAW,QAAQ,KAAK,YAAY,OAAO,OAAO,KAAK;AAAA,MACvE,CAAC;AACD,UAAI,qBAAqB;AACvB,4BAAoB,aAAa,IAAI;AAAA,MACvC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,2BAA2B;AACzB,WAAO,KAAK,QAAQ,OAAO,YAAU,OAAO,QAAQ,EAAE,IAAI,YAAU,OAAO,KAAK;AAAA,EAClF;AAAA;AAAA,EAEA,uBAAuB;AACrB,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,QAAQ,YAAU,OAAO,cAAc,CAAC;AAAA,IACvD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,YAAY,cAAc;AAG/C,UAAM,iBAAiB,CAAC;AACxB,SAAK,QAAQ,QAAQ,YAAU;AAC7B,WAAK,CAAC,gBAAgB,CAAC,OAAO,aAAa,OAAO,aAAa,UAAU,GAAG;AAC1E,uBAAe,KAAK,MAAM;AAAA,MAC5B;AAAA,IACF,CAAC;AACD,QAAI,eAAe,QAAQ;AACzB,WAAK,mBAAmB;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,UAAM,aAAa,KAAK,YAAY;AACpC,SAAK,MAAM,YAAY,SAAS,MAAM,YAAY,UAAU,CAAC,KAAK,YAAY,SAAS,KAAK,cAAc,CAAC,WAAW,UAAU;AAC9H,YAAM,eAAe;AACrB,iBAAW,qBAAqB;AAAA,IAClC,WAAW,MAAM,YAAY,KAAK,KAAK,YAAY,CAAC,KAAK,YAAY,SAAS,KAAK,eAAe,OAAO,WAAW,SAAS,GAAG;AAC9H,YAAM,eAAe,KAAK,QAAQ,KAAK,YAAU,CAAC,OAAO,YAAY,CAAC,OAAO,QAAQ;AACrF,YAAM,eAAe;AACrB,WAAK,iBAAiB,KAAK,uBAAuB,cAAc,IAAI,CAAC;AAAA,IACvE,OAAO;AACL,WAAK,YAAY,UAAU,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB,MAAM;AAEtB,eAAW,MAAM;AACf,UAAI,CAAC,KAAK,eAAe,GAAG;AAC1B,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,iBAAiB,WAAS;AACxB,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AACA,UAAM,cAAc,KAAK,OAAO,QAAQ,EAAE,UAAU,UAAQ,KAAK,YAAY,cAAc,SAAS,MAAM,MAAM,CAAC;AACjH,QAAI,cAAc,IAAI;AACpB,WAAK,iBAAiB,WAAW;AAAA,IACnC,OAAO;AACL,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,uBAAuB;AACrB,SAAK,cAAc,IAAI,gBAAgB,KAAK,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,SAAS,EAAE,cAAc,MAAM,KAAK,QAAQ;AAEjI,SAAK,mBAAmB;AAExB,SAAK,YAAY,OAAO,UAAU,qBAAmB,KAAK,iBAAiB,eAAe,CAAC;AAE3F,SAAK,OAAO,QAAQ,SAAK,4BAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AACnE,YAAM,aAAa,KAAK,YAAY;AACpC,UAAI,CAAC,cAAc,KAAK,OAAO,QAAQ,EAAE,QAAQ,UAAU,MAAM,IAAI;AACnE,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,OAAO;AACtB,SAAK,OAAO,QAAQ,CAAC,MAAM,cAAc,KAAK,aAAa,cAAc,QAAQ,IAAI,EAAE,CAAC;AACxF,SAAK,YAAY,iBAAiB,KAAK;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,QAAI,KAAK,UAAU;AACjB,WAAK,iBAAiB,EAAE;AACxB;AAAA,IACF;AACA,UAAM,aAAa,KAAK,OAAO,KAAK,UAAQ,KAAK,YAAY,CAAC,KAAK,QAAQ,KAAK,KAAK,OAAO;AAC5F,SAAK,iBAAiB,aAAa,KAAK,OAAO,QAAQ,EAAE,QAAQ,UAAU,IAAI,EAAE;AAAA,EACnF;AAAA;AAAA,EAEA,iBAAiB;AACf,UAAM,gBAAgB,kCAAkC;AACxD,WAAO,iBAAiB,KAAK,SAAS,cAAc,SAAS,aAAa;AAAA,EAC5E;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,IAClC,gBAAgB,SAAS,gCAAgC,IAAI,KAAK,UAAU;AAC1E,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,SAAS;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,QAAQ,WAAW,GAAG,0BAA0B,qBAAqB,UAAU;AAAA,IAC3F,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,SAAS,4CAA4C,QAAQ;AACpF,iBAAO,IAAI,eAAe,MAAM;AAAA,QAClC,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,wBAAwB,IAAI,QAAQ;AAAA,MACrD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,MACb,UAAU;AAAA,MACV,8BAA8B;AAAA,MAC9B,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,UAAU,CAAC,kBAAkB;AAAA,IAC7B,UAAU,CAAI,mBAAmB,CAAC,mCAAmC;AAAA,MACnE,SAAS;AAAA,MACT,aAAa;AAAA,IACf,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,4BAA+B,oBAAoB;AAAA,IAC3D,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB;AACnB,QAAG,aAAa,CAAC;AAAA,MACnB;AAAA,IACF;AAAA,IACA,QAAQ,CAAC,GAAG;AAAA,IACZ,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,+BAA+B;AAAA,QAC/B,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,WAAW,CAAC,mCAAmC;AAAA,QAC7C,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,+kjBAAqmjB;AAAA,IAChnjB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,QACpB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,iBAAiB,iBAAiB,yBAAyB,SAAS,eAAe,YAAY,kBAAkB,aAAa,eAAe,8BAA8B,mBAAmB,iBAAiB,iBAAiB,kBAAkB,eAAe;AAAA,IAC5R,SAAS,CAAC,SAAS,eAAe,YAAY,kBAAkB,aAAa,eAAe,mBAAmB,iBAAiB,8BAA8B,kBAAkB,iBAAiB,kBAAkB,eAAe;AAAA,EACpO,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,iBAAiB,iBAAiB,yBAAyB,gBAAgB;AAAA,EACxG,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB,iBAAiB,yBAAyB,SAAS,eAAe,YAAY,kBAAkB,aAAa,eAAe,8BAA8B,mBAAmB,iBAAiB,iBAAiB,kBAAkB,eAAe;AAAA,MAC5R,SAAS,CAAC,SAAS,eAAe,YAAY,kBAAkB,aAAa,eAAe,mBAAmB,iBAAiB,8BAA8B,kBAAkB,iBAAiB,kBAAkB,eAAe;AAAA,IACpO,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["import_rxjs", "import_rxjs"]}