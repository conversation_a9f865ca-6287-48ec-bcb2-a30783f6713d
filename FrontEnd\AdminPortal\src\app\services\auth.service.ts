import { Injectable, signal, computed, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import { LoginModel } from '@models/LoginModel';
import { ForgotPasswordModel } from '@models/ForgotPasswordModel';
import { VerifyOtpModel } from '@models/VerifyOtpModel';
import { ResetPasswordModel } from '@models/ResetPasswordModel';
import { LoginApiResponse, ForgotPasswordApiResponse, VerifyOtpApiResponse, ResetPasswordApiResponse } from '@models/ApiResponse';
import { environment } from '../../environments/environment';

export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
}


@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly STORAGE_KEY = 'invofy_auth_token';
  private readonly USER_KEY = 'invofy_user_data';
  


  // Authentication state signals
  private _isAuthenticated = signal<boolean>(false);
  private _currentUser = signal<User | null>(null);
  private _token = signal<string | null>(null);
  private _isLoading = signal<boolean>(false);

  // Public computed signals
  isAuthenticated = computed(() => this._isAuthenticated());
  currentUser = computed(() => this._currentUser());
  isLoading = computed(() => this._isLoading());

  constructor(
    private http: HttpClient,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.initializeAuthState();
  }

  /**
   * Check if we're running in the browser
   */
  private isBrowser(): boolean {
    return isPlatformBrowser(this.platformId);
  }

  /**
   * Initialize authentication state from localStorage
   */
  private initializeAuthState(): void {
    if (!this.isBrowser()) {
      return; // Skip initialization on server
    }

    try {
      const token = localStorage.getItem(this.STORAGE_KEY);
      const userData = localStorage.getItem(this.USER_KEY);

      if (token && userData) {
        const user = JSON.parse(userData);
        this._token.set(token);
        this._currentUser.set(user);
        this._isAuthenticated.set(true);
      }
    } catch (error) {
      console.error('Error initializing auth state:', error);
      this.clearAuthState();
    }
  }

  /**
   * Login with userName and password
   */
  async login(credentials: LoginModel): Promise<LoginApiResponse> {
    this._isLoading.set(true);

    try {
      const response = await new Promise<LoginApiResponse>((resolve, reject) => {
        this.http.post<LoginApiResponse>(
          `${environment.apiBaseUrl}/Auth/Login`,
          credentials
        ).subscribe({
          next: (data) => resolve(data),
          error: (error) => reject(error)
        });
      });

      if (response?.isSuccess && response.data) {
        const { token, user } = response.data;

        // Set authentication state
        this._token.set(token);
        this._currentUser.set(user);
        this._isAuthenticated.set(true);

        // Persist to localStorage (only in browser)
        if (this.isBrowser()) {
          localStorage.setItem(this.STORAGE_KEY, token);
          localStorage.setItem(this.USER_KEY, JSON.stringify(user));
        }

        this._isLoading.set(false);
        return response;
      } else {
        this._isLoading.set(false);
        return response;
      }
    } catch (error) {
      this._isLoading.set(false);

      if (error instanceof HttpErrorResponse) {
        // Handle HTTP errors
        if (error.status === 401) {
          return {
            isSuccess: false,
            message: 'Invalid username or password',
            stateCode: error.status
          };
        } else if (error.status === 0) {
          return {
            isSuccess: false,
            message: 'Unable to connect to server. Please check your connection.',
            stateCode: error.status

          };
        } else {
          return {
            isSuccess: false,
            message: error.error?.message || 'An error occurred during login',
            stateCode: error.status 
          };
        }
      }

      return {
        isSuccess: false,
        message: 'An unexpected error occurred during login',
        stateCode: 500
      };
    }
  }

  /**
   * Verify OTP for password reset
   */
  async verifyOtp(otpData: VerifyOtpModel): Promise<{ success: boolean; message: string; token?: string }> {
    try {
      const response = await new Promise<VerifyOtpApiResponse>((resolve, reject) => {
        this.http.post<VerifyOtpApiResponse>(
          `${environment.apiBaseUrl}/Auth/VerifyPasswordResetOtp`,
          otpData
        ).subscribe({
          next: (data) => resolve(data),
          error: (error) => reject(error)
        });
      });

      return {
        success: response.isSuccess,
        message: response.message || 'OTP verified successfully',
        token: response.data?.token
      };
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        // Handle HTTP errors
        if (error.status === 400) {
          return {
            success: false,
            message: 'Invalid or expired OTP'
          };
        } else if (error.status === 404) {
          return {
            success: false,
            message: 'Email address not found'
          };
        } else if (error.status === 0) {
          return {
            success: false,
            message: 'Unable to connect to server. Please check your connection.'
          };
        } else {
          return {
            success: false,
            message: error.error?.message || 'An error occurred while verifying OTP'
          };
        }
      }

      return {
        success: false,
        message: 'An unexpected error occurred while verifying OTP'
      };
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(resetData: ResetPasswordModel): Promise<{ success: boolean; message: string }> {
    try {
      const response = await new Promise<ResetPasswordApiResponse>((resolve, reject) => {
        this.http.post<ResetPasswordApiResponse>(
          `${environment.apiBaseUrl}/Auth/ResetPassword`,
          resetData
        ).subscribe({
          next: (data) => resolve(data),
          error: (error) => reject(error)
        });
      });

      return {
        success: response.isSuccess,
        message: response.message || 'Password reset successfully'
      };
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        // Handle HTTP errors
        if (error.status === 400) {
          return {
            success: false,
            message: 'Invalid or expired reset token'
          };
        } else if (error.status === 422) {
          return {
            success: false,
            message: 'Password does not meet requirements'
          };
        } else if (error.status === 0) {
          return {
            success: false,
            message: 'Unable to connect to server. Please check your connection.'
          };
        } else {
          return {
            success: false,
            message: error.error?.message || 'An error occurred while resetting password'
          };
        }
      }

      return {
        success: false,
        message: 'An unexpected error occurred while resetting password'
      };
    }
  }

  /**
   * Send forgot password request
   */
  async forgotPassword(email: ForgotPasswordModel): Promise<{ success: boolean; message: string }> {
    try {
      const response = await new Promise<ForgotPasswordApiResponse>((resolve, reject) => {
        this.http.post<ForgotPasswordApiResponse>(
          `${environment.apiBaseUrl}/Auth/ForgotPassword`,
          email
        ).subscribe({
          next: (data) => resolve(data),
          error: (error) => reject(error)
        });
      });

      return {
        success: response.isSuccess,
        message: response.message || 'Password reset email sent successfully'
      };
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        // Handle HTTP errors
        if (error.status === 404) {
          return {
            success: false,
            message: 'Email address not found'
          };
        } else if (error.status === 0) {
          return {
            success: false,
            message: 'Unable to connect to server. Please check your connection.'
          };
        } else {
          return {
            success: false,
            message: error.error?.message || 'An error occurred while sending reset email'
          };
        }
      }

      return {
        success: false,
        message: 'An unexpected error occurred while sending reset email'
      };
    }
  }

  /**
   * Logout user and clear authentication state
   */
  async logout(): Promise<void> {
    this.clearAuthState();
    // Use Promise to ensure state is cleared before navigation
    await new Promise(resolve => setTimeout(resolve, 50));
    await this.router.navigate(['/auth/login']);
  }

  /**
   * Clear all authentication state
   */
  private clearAuthState(): void {
    this._isAuthenticated.set(false);
    this._currentUser.set(null);
    this._token.set(null);

    // Clear localStorage (only in browser)
    if (this.isBrowser()) {
      localStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(this.USER_KEY);
    }
  }



  /**
   * Get the current authentication token
   */
  getToken(): string | null {
    return this._token();
  }

  /**
   * Check if the current token is valid (for future API integration)
   */
  isTokenValid(): boolean {
    const token = this._token();
    if (!token) return false;

    try {
      // For mock token, just check if it exists
      // In real implementation, you would decode and check expiration
      const parts = token.split('.');
      if (parts.length !== 3) return false;

      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      
      return payload.exp > currentTime;
    } catch (error) {
      return false;
    }
  }

  /**
   * Refresh token (placeholder for future API integration)
   */
  async refreshToken(): Promise<boolean> {
    // Placeholder for future implementation
    return this.isTokenValid();
  }
}
