import './polyfills.server.mjs';
var i=Object.defineProperty,j=Object.defineProperties;var k=Object.getOwnPropertyDescriptors;var e=Object.getOwnPropertySymbols;var g=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable;var f=(a,b,c)=>b in a?i(a,b,{enumerable:!0,configurable:!0,writable:!0,value:c}):a[b]=c,l=(a,b)=>{for(var c in b||={})g.call(b,c)&&f(a,c,b[c]);if(e)for(var c of e(b))h.call(b,c)&&f(a,c,b[c]);return a},m=(a,b)=>j(a,k(b));var n=(a=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(a,{get:(b,c)=>(typeof require<"u"?require:b)[c]}):a)(function(a){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+a+'" is not supported')});var o=(a,b)=>{var c={};for(var d in a)g.call(a,d)&&b.indexOf(d)<0&&(c[d]=a[d]);if(a!=null&&e)for(var d of e(a))b.indexOf(d)<0&&h.call(a,d)&&(c[d]=a[d]);return c};var p=(a,b)=>()=>(b||a((b={exports:{}}).exports,b),b.exports);export{l as a,m as b,n as c,o as d,p as e};
