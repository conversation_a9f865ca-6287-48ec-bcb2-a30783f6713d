import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';

import { VerifyOtp } from './verify-otp';
import { AuthService } from '@services/auth.service';

describe('VerifyOtp', () => {
  let component: VerifyOtp;
  let fixture: ComponentFixture<VerifyOtp>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    mockAuthService = jasmine.createSpyObj('AuthService', ['verifyOtp', 'forgotPassword']);
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockActivatedRoute = {
      queryParams: of({ email: '<EMAIL>' })
    };

    await TestBed.configureTestingModule({
      imports: [VerifyOtp],
      providers: [
        { provide: AuthService, useValue: mockAuthService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(VerifyOtp);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with 60 second countdown', () => {
    expect(component.resendCountdown()).toBe(60);
    expect(component.isResendDisabled()).toBe(true);
  });

  it('should update countdown text correctly', () => {
    component.resendCountdown.set(30);
    expect(component.resendButtonText()).toBe('Resend in 30s');

    component.resendCountdown.set(0);
    expect(component.resendButtonText()).toBe("Didn't receive code? Resend");
  });

  it('should enable resend button when countdown reaches 0', fakeAsync(() => {
    component.resendCountdown.set(2);
    tick(2000);
    expect(component.resendCountdown()).toBe(0);
    expect(component.isResendDisabled()).toBe(false);
  }));

  it('should start countdown after successful resend', fakeAsync(() => {
    // Set countdown to 0 to enable resend
    component.resendCountdown.set(0);

    // Mock successful resend
    mockAuthService.forgotPassword.and.returnValue(
      Promise.resolve({ success: true, message: 'OTP resent successfully' })
    );

    // Call resend
    component.onResendOtp();
    tick();

    // Verify countdown started
    expect(component.resendCountdown()).toBe(60);
  }));
});
