<div class="otp-input-container">
  @if (label()) {
    <label class="otp-label">{{ label() }}</label>
  }
  
  <div class="otp-inputs-wrapper" [attr.aria-label]="label() || 'OTP Input'">
    @for (index of getIndices(); track index) {
      <mat-form-field appearance="outline" class="otp-digit-field">
        <input
          #otpInput
          matInput
          type="text"
          inputmode="numeric"
          pattern="[0-9]*"
          maxlength="1"
          [value]="otpDigits()[index]"
          [name]="name() + '_' + index"
          [required]="required()"
          [disabled]="disabled()"
          [autocomplete]="index === 0 ? autocomplete() : 'off'"
          [attr.aria-label]="'Digit ' + (index + 1) + ' of ' + length()"
          [attr.data-index]="index"
          (input)="onInputChange($event, index)"
          (keydown)="onKeyDown($event, index)"
          (paste)="onPaste($event, index)"
          (focus)="onFocus($event)"
          class="otp-digit-input">
      </mat-form-field>
    }
  </div>
  
  @if (placeholder() && shouldShowPlaceholder()) {
    <div class="otp-placeholder">{{ placeholder() }}</div>
  }
</div>
