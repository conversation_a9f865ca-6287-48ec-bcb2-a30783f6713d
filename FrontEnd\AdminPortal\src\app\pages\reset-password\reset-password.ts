import { Component, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthCard } from '@components/auth-card/auth-card';
import { PasswordInput } from '@components/password-input/password-input';
import { PrimaryButton } from '@components/primary-button/primary-button';
import { AuthService } from '@services/auth.service';
import { ResetPasswordModel } from '@models/ResetPasswordModel';

@Component({
  selector: 'app-reset-password',
  imports: [
        CommonModule,
        AuthCard,
        PasswordInput,
        PrimaryButton
  ],
  templateUrl: './reset-password.html',
  styleUrl: './reset-password.scss'
})
export class ResetPassword {
  password = signal<string>('');
  confirmPassword = signal<string>('');
  isLoading = signal<boolean>(false);
  successMessage = signal<string>('');
  errorMessage = signal<string>('');
  token = signal<string>('');
  email = signal<string>('');

  resetPasswordData = computed<ResetPasswordModel>(() => ({
    token: this.token(),
    newPassword: this.password()
  }));

  isValidForm = computed<boolean>(() => {
    const hasPasswords = !!(this.password() && this.confirmPassword());
    const passwordsMatch = this.password() === this.confirmPassword();
    const hasToken = !!this.token();
    const isPasswordStrong = this.isPasswordStrong();

    return hasPasswords && passwordsMatch && hasToken && isPasswordStrong;
  });

  passwordsMatch = computed<boolean>(() =>
    this.password() === this.confirmPassword() || !this.confirmPassword()
  );

  isPasswordStrong = computed<boolean>(() => {
    const password = this.password();
    if (!password) return false;

    // Password requirements: at least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
    const minLength = password.length >= 8;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return minLength && hasUppercase && hasLowercase && hasNumber && hasSpecialChar;
  });

  constructor(
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    // Get token and email from query parameters
    this.route.queryParams.subscribe(params => {
      if (params['token']) {
        this.token.set(params['token']);
      }
      if (params['email']) {
        this.email.set(params['email']);
      }

      // If no token is provided, redirect to forgot password
      if (!params['token'] && !params['verified']) {
        this.router.navigate(['/auth/forgot-password']);
      }
    });
  }



  onPasswordCodeChange(value: string): void {
    this.password.set(value);
    // Clear messages when user starts typing
    this.errorMessage.set('');
    this.successMessage.set('');
  }

  onConfirmPasswordCodeChange(value: string): void {
    this.confirmPassword.set(value);
    // Clear messages when user starts typing
    this.errorMessage.set('');
    this.successMessage.set('');
  }

  async onSubmit(): Promise<void> {
    if (this.isValidForm()) {
      this.isLoading.set(true);
      this.errorMessage.set('');
      this.successMessage.set('');

      try {
        const result = await this.authService.resetPassword(this.resetPasswordData());

        if (result.success) {
          this.successMessage.set(result.message);
          // Redirect to login page after successful reset
          setTimeout(() => {
            this.router.navigate(['/auth/login']);
          }, 3000);
        } else {
          this.errorMessage.set(result.message);
        }
      } catch (error) {
        this.errorMessage.set('An unexpected error occurred. Please try again.');
      } finally {
        this.isLoading.set(false);
      }
    }
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }
}
