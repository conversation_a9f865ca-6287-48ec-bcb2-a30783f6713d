import { Component, computed, input, output } from '@angular/core';

import {MatIconModule} from '@angular/material/icon';
import {  MatSidenavModule } from '@angular/material/sidenav';
import { RouterModule } from '@angular/router';
import {MatListModule} from '@angular/material/list';
import {MatButtonModule} from '@angular/material/button';

import sidebarMenus from '@utils/data/sidebar-menus';
import { AuthService } from '@services/auth.service';

@Component({
  selector: 'app-sidebar',
  imports: [MatIconModule,MatListModule ,RouterModule,MatSidenavModule,MatButtonModule],
  templateUrl: './sidebar.html',
  styleUrl: './sidebar.scss'
})
export class Sidebar {
  onToggleSidebar = output<void>();
  sidebarCollapsed = input.required<boolean>();
  matIconClass = computed(() => !this.sidebarCollapsed() ? 'keyboard_tab' : 'keyboard_tab_rtl');

  sidebarMenus = sidebarMenus;

  constructor(private authService: AuthService) {}

  togglerClick(): void {
      this.onToggleSidebar.emit();
  }

  async onLogout(): Promise<void> {
    await this.authService.logout();
  }
}
