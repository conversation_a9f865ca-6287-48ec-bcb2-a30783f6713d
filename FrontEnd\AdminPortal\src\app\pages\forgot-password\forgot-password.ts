import { Component, signal, computed } from '@angular/core';
import { Router } from '@angular/router';
import { AuthCard } from '@components/auth-card/auth-card';
import { FormInput } from '@components/form-input/form-input';
import { PrimaryButton } from '@components/primary-button/primary-button';
import { AuthService } from '@services/auth.service';
import { ForgotPasswordModel } from '@models/ForgotPasswordModel';

@Component({
  selector: 'app-forgot-password',
imports: [
      AuthCard,
      FormInput,
      PrimaryButton
    ],
  templateUrl: './forgot-password.html',
  styleUrl: './forgot-password.scss'
})
export class ForgotPassword {
  email = signal<string>('');
  isLoading = signal<boolean>(false);
  successMessage = signal<string>('');
  errorMessage = signal<string>('');

  forgotPasswordData = computed<ForgotPasswordModel>(() => ({
    email: this.email()
  }));

  isValidForm = computed<boolean>(() =>
    !!(this.email() && this.isValidEmail())
  );

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  private isValidEmail(): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(this.email());
  }

  onEmailChange(value: string): void {
    this.email.set(value);
    // Clear messages when user starts typing
    this.errorMessage.set('');
    this.successMessage.set('');
  }

  async onSubmit(): Promise<void> {
    if (this.isValidForm()) {
      this.isLoading.set(true);
      this.errorMessage.set('');
      this.successMessage.set('');

      try {
        const result = await this.authService.forgotPassword(this.forgotPasswordData());

        if (result.success) {
          this.successMessage.set(result.message);
          // Redirect to OTP verification page
          setTimeout(() => {
            this.router.navigate(['/auth/verify-otp'], {
              queryParams: { email: this.email() }
            });
          }, 2000);
        } else {
          this.errorMessage.set(result.message);
        }
      } catch (error) {
        this.errorMessage.set('An unexpected error occurred. Please try again.');
      } finally {
        this.isLoading.set(false);
      }
    }
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }
}
