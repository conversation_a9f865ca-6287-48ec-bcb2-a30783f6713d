<div class="sidebar flex flex-col">
    <div class="sidebar-header">
        @if(sidebarCollapsed())
        {
            <img   src="../../../assets/svgs/invofy-pro-logo.svg" alt="Invofy Logo">
        } 
        <button (click)="togglerClick()" class="pt-2 px-2 rounded-full cursor-pointer " mat-icon-button  matSuffix type="button" >
            <mat-icon class="material-symbols-outlined text-lg">{{matIconClass()}}</mat-icon>
        </button>
    </div>
    <div class="sidebar-content flex-grow overflow-y-auto">
        <mat-nav-list class="!grid gap-2" >
            @for(sideItem of sidebarMenus; track $index) {
                <mat-list-item [routerLinkActiveOptions]="{ exact: true }" [routerLink]="sideItem.route" routerLinkActive="sidebar-link-active" #rla="routerLinkActive" [activated]="rla.isActive">
                <mat-icon matListItemIcon class="material-symbols-outlined text-lg">{{sideItem.icon}}</mat-icon>
                    <span matListItemTitle>{{sideItem.label}}</span>
                </mat-list-item>
            }
        </mat-nav-list>
    </div>
    <div class="flex-shrink-0  sidebar-footer">
         <mat-nav-list class="!grid gap-2" > 
            <mat-list-item >
                <mat-icon matListItemIcon class="material-symbols-outlined text-lg">person</mat-icon>
                <span matListItemTitle>My Account</span>
            </mat-list-item> 
             <mat-list-item >
                <mat-icon matListItemIcon class="material-symbols-outlined text-lg">help</mat-icon>
                <span matListItemTitle>Contact Us</span>
            </mat-list-item> 
             <mat-list-item (click)="onLogout()" class="cursor-pointer">
                <mat-icon matListItemIcon class="material-symbols-outlined text-lg">logout</mat-icon>
                <span matListItemTitle>Logout</span>
            </mat-list-item>
        </mat-nav-list>
    </div>
</div>

