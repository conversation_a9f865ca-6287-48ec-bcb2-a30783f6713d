
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: [
  {
    "renderMode": 1,
    "route": "/"
  },
  {
    "renderMode": 1,
    "route": "/invoices"
  },
  {
    "renderMode": 1,
    "route": "/items"
  },
  {
    "renderMode": 1,
    "route": "/reports"
  },
  {
    "renderMode": 1,
    "route": "/store-setting"
  },
  {
    "renderMode": 1,
    "redirectTo": "/auth/login",
    "route": "/auth"
  },
  {
    "renderMode": 1,
    "route": "/auth/login"
  },
  {
    "renderMode": 1,
    "route": "/auth/mfa"
  },
  {
    "renderMode": 1,
    "route": "/auth/forgot-password"
  },
  {
    "renderMode": 1,
    "route": "/auth/verify-otp"
  },
  {
    "renderMode": 1,
    "route": "/auth/reset-password"
  }
],
  entryPointToBrowserMapping: undefined,
  assets: {
    'index.csr.html': {size: 42005, hash: '80415f7bcfac233525243a5c6257220acc30ab0a76fff174e72c139b48f4d8dd', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 17760, hash: '802744c4c1a799167da365f2b1e253413901faaebfcf81f778845cfd60619e68', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-Q7WKFUMX.css': {size: 34216, hash: '3qe2ie6MnO0', text: () => import('./assets-chunks/styles-Q7WKFUMX_css.mjs').then(m => m.default)}
  },
};
