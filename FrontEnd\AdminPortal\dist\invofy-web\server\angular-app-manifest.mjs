
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: [
  {
    "renderMode": 1,
    "route": "/"
  },
  {
    "renderMode": 1,
    "route": "/invoices"
  },
  {
    "renderMode": 1,
    "route": "/items"
  },
  {
    "renderMode": 1,
    "route": "/reports"
  },
  {
    "renderMode": 1,
    "route": "/store-setting"
  },
  {
    "renderMode": 1,
    "redirectTo": "/auth/login",
    "route": "/auth"
  },
  {
    "renderMode": 1,
    "route": "/auth/login"
  },
  {
    "renderMode": 1,
    "route": "/auth/mfa"
  },
  {
    "renderMode": 1,
    "route": "/auth/forgot-password"
  },
  {
    "renderMode": 1,
    "route": "/auth/verify-otp"
  },
  {
    "renderMode": 1,
    "route": "/auth/reset-password"
  }
],
  entryPointToBrowserMapping: undefined,
  assets: {
    'index.csr.html': {size: 42050, hash: '1d10c736192d5e5771d26964829135c40e76a819e7698cef5eac51f6108afa3c', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 17760, hash: '6591e9eac38855151d79c2aa6a5e0c0f702f0354cfea73d81e27d2adb5b9d450', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-B6VY6J6E.css': {size: 34035, hash: 'Ltd+AF0CQsk', text: () => import('./assets-chunks/styles-B6VY6J6E_css.mjs').then(m => m.default)}
  },
};
