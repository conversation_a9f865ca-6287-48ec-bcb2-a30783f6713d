# OTP Input Component

A reusable, accessible OTP (One-Time Password) input component built with Angular Material that provides separate input fields for each digit with advanced keyboard navigation and user experience features.

## Overview

The OTP Input component provides a modern, user-friendly interface for entering verification codes with:
- Individual input fields for each digit (default 6 digits)
- Smart keyboard navigation and auto-focus
- Paste support for complete OTP codes
- Angular Material integration with consistent styling
- Full accessibility support
- Responsive design for all screen sizes

## Features

### 🎯 **Core Functionality**
- **Separate Digit Inputs**: Individual Material input fields for each OTP digit
- **Configurable Length**: Support for 4, 6, 8, or any custom length OTP codes
- **Numeric Only**: Automatically filters non-numeric input
- **Auto-focus**: Automatically moves focus to next/previous inputs

### ⌨️ **Advanced Keyboard Support**
- **Arrow Navigation**: Left/Right arrows to move between inputs
- **Backspace Handling**: Smart backspace behavior (clear current or move to previous)
- **Delete Key**: Clear current input without moving focus
- **Tab Navigation**: Standard tab order for accessibility
- **Paste Support**: Intelligent paste handling for complete OTP codes

### 🎨 **Material Design Integration**
- **Angular Material**: Built with `mat-form-field` and `matInput`
- **Outline Appearance**: Consistent with other form components
- **Focus States**: Material Design focus indicators
- **Error States**: Built-in error styling support
- **Disabled States**: Proper disabled styling and behavior

### ♿ **Accessibility Features**
- **ARIA Labels**: Comprehensive labeling for screen readers
- **Semantic HTML**: Proper form structure and roles
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Logical focus flow and indicators
- **Screen Reader Support**: Descriptive announcements

## Usage Examples

### Basic Implementation
```html
<app-otp-input
  [label]="'Verification Code'"
  [placeholder]="'Enter 6-digit code'"
  [value]="otpValue()"
  (valueChange)="onOtpChange($event)"
  (complete)="onOtpComplete($event)">
</app-otp-input>
```

### Advanced Configuration
```html
<app-otp-input
  [label]="'MFA Code'"
  [placeholder]="'Enter authenticator code'"
  [name]="'mfaCode'"
  [value]="mfaCode()"
  [required]="true"
  [disabled]="isLoading()"
  [length]="6"
  [autocomplete]="'one-time-code'"
  (valueChange)="onMfaCodeChange($event)"
  (complete)="onMfaCodeComplete($event)">
</app-otp-input>
```

### Custom Length (4-digit PIN)
```html
<app-otp-input
  [label]="'PIN Code'"
  [placeholder]="'Enter 4-digit PIN'"
  [length]="4"
  [value]="pinCode()"
  (valueChange)="onPinChange($event)">
</app-otp-input>
```

## Component API

### Input Properties
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `label` | `string` | `'Verification Code'` | Label text displayed above inputs |
| `placeholder` | `string` | `'Enter 6-digit code'` | Placeholder text shown when empty |
| `name` | `string` | `'otp'` | Base name for form inputs |
| `required` | `boolean` | `false` | Whether the input is required |
| `value` | `string` | `''` | Current OTP value |
| `disabled` | `boolean` | `false` | Whether inputs are disabled |
| `autocomplete` | `string` | `'one-time-code'` | Autocomplete attribute |
| `length` | `number` | `6` | Number of OTP digits |

### Output Events
| Event | Type | Description |
|-------|------|-------------|
| `valueChange` | `string` | Emitted when OTP value changes |
| `complete` | `string` | Emitted when all digits are filled |

### Public Methods
| Method | Description |
|--------|-------------|
| `clear()` | Clears all inputs and focuses first field |

## Component Integration

### TypeScript Implementation
```typescript
import { Component, signal } from '@angular/core';
import { OtpInput } from '@components/otp-input/otp-input';

@Component({
  selector: 'app-verify-otp',
  imports: [OtpInput],
  template: `
    <app-otp-input
      [value]="otpCode()"
      (valueChange)="onOtpChange($event)"
      (complete)="onOtpComplete($event)">
    </app-otp-input>
  `
})
export class VerifyOtpComponent {
  otpCode = signal<string>('');

  onOtpChange(value: string): void {
    this.otpCode.set(value);
  }

  onOtpComplete(value: string): void {
    // Auto-submit when complete
    if (value.length === 6) {
      this.submitOtp(value);
    }
  }
}
```

## Styling Customization

### CSS Custom Properties
```scss
.otp-input-container {
  --otp-digit-width: 48px;
  --otp-digit-height: 56px;
  --otp-gap: 12px;
  --otp-border-color: var(--ivy-neutral-300);
  --otp-focus-color: var(--ivy-sky-blue);
  --otp-error-color: var(--ivy-red-500);
}
```

### Responsive Breakpoints
```scss
// Mobile (< 480px)
.otp-digit-field {
  width: 40px;
  height: 48px;
}

// Small mobile (< 360px)
.otp-digit-field {
  width: 36px;
  height: 44px;
}
```

## Browser Support

- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile Browsers**: iOS Safari 14+, Chrome Mobile 90+
- **Input Methods**: Physical keyboards, virtual keyboards, voice input
- **Assistive Technology**: Screen readers, keyboard navigation

## Accessibility Compliance

### WCAG 2.1 AA Standards
- ✅ **Color Contrast**: 4.5:1 minimum contrast ratio
- ✅ **Keyboard Navigation**: Full keyboard accessibility
- ✅ **Focus Indicators**: Visible focus states
- ✅ **Screen Reader**: Comprehensive ARIA labeling
- ✅ **Touch Targets**: Minimum 44px touch targets

### ARIA Implementation
```html
<input
  matInput
  [attr.aria-label]="'Digit ' + (index + 1) + ' of ' + length()"
  [attr.data-index]="index"
  role="textbox"
  aria-required="true">
```

## Performance Optimizations

### Angular Signals
- Reactive state management with minimal re-renders
- Computed properties for derived state
- Efficient change detection

### Memory Management
- Proper cleanup of event listeners
- ViewChildren lifecycle management
- No memory leaks in component destruction

## Testing

### Unit Tests
```typescript
it('should handle paste correctly', () => {
  const mockEvent = {
    preventDefault: jasmine.createSpy('preventDefault'),
    clipboardData: {
      getData: jasmine.createSpy('getData').and.returnValue('123456')
    }
  } as any;

  component.onPaste(mockEvent, 0);
  
  expect(mockEvent.preventDefault).toHaveBeenCalled();
  expect(component.otpDigits()).toEqual(['1', '2', '3', '4', '5', '6']);
});
```

### Manual Testing Checklist
- [ ] Individual digit input works correctly
- [ ] Auto-focus moves to next input
- [ ] Backspace moves to previous input
- [ ] Arrow keys navigate between inputs
- [ ] Paste functionality works with complete codes
- [ ] Component handles disabled state
- [ ] Error states display correctly
- [ ] Responsive design works on mobile
- [ ] Screen reader announces properly
- [ ] Keyboard navigation is logical

## Common Use Cases

### 1. Email Verification
```html
<app-otp-input
  [label]="'Email Verification'"
  [placeholder]="'Check your email for code'"
  [length]="6">
</app-otp-input>
```

### 2. Two-Factor Authentication
```html
<app-otp-input
  [label]="'Authenticator Code'"
  [placeholder]="'Enter 6-digit code from app'"
  [length]="6">
</app-otp-input>
```

### 3. SMS Verification
```html
<app-otp-input
  [label]="'SMS Code'"
  [placeholder]="'Enter code from SMS'"
  [autocomplete]="'one-time-code'"
  [length]="6">
</app-otp-input>
```

### 4. PIN Entry
```html
<app-otp-input
  [label]="'PIN'"
  [placeholder]="'Enter your PIN'"
  [length]="4">
</app-otp-input>
```

## Troubleshooting

### Common Issues
1. **Focus not moving**: Check if inputs are properly rendered
2. **Paste not working**: Verify clipboard permissions
3. **Styling issues**: Ensure Angular Material styles are imported
4. **Mobile keyboard**: Use `inputmode="numeric"` for numeric keyboards

### Debug Tips
- Use browser dev tools to inspect ARIA attributes
- Test with screen readers for accessibility
- Verify keyboard navigation with Tab key
- Check mobile responsiveness on actual devices
