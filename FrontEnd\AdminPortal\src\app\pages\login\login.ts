import { Component, signal, computed, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { Router } from '@angular/router';
import { AuthCard } from '@components/auth-card/auth-card';
import { FormInput } from '@components/form-input/form-input';
import { PasswordInput } from '@components/password-input/password-input';
import { PrimaryButton } from '@components/primary-button/primary-button';
import { LoginModel } from '@models/LoginModel';
import { AuthService } from '@services/auth.service';

@Component({
  selector: 'app-login',
  imports: [
    AuthCard,
    FormInput,
    PasswordInput,
    PrimaryButton
  ],
  templateUrl: './login.html',
  styleUrl: './login.scss'
})
export class Login {
  userName = signal<string>('');
  password = signal<string>('');
  errorMessage = signal<string>('');

  loginData = computed<LoginModel>(() => ({
    userName: this.userName(),
    password: this.password()
  }));

  isValidForm = computed<boolean>(() =>
    !!(this.userName() && this.password())
  );

  // Get loading state from AuthService
  isLoading = computed(() => this.authService.isLoading());

  constructor(
    private authService: AuthService,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  onUserNameChange(value: string): void {
    this.userName.set(value);
  }

  onPasswordChange(value: string): void {
    this.password.set(value);
  }

  async onLogin(): Promise<void> {
    if (this.isValidForm()) {
      this.errorMessage.set('');

      try {
        const result = await this.authService.login(this.loginData());

        if (result.isSuccess) { 
          let redirectUrl = '/';
          if (isPlatformBrowser(this.platformId)) {
            redirectUrl = localStorage.getItem('invofy_redirect_url') || '/';
            localStorage.removeItem('invofy_redirect_url');
          }
          this.router.navigate([redirectUrl]);
        } else {
          this.errorMessage.set(result.message);
        }
      } catch (error) {
        this.errorMessage.set('An unexpected error occurred. Please try again.');
      }
    }
  }

  onForgotPassword(event: Event): void {
    event.preventDefault();
    this.router.navigate(['/auth/forgot-password']);
  }
}
